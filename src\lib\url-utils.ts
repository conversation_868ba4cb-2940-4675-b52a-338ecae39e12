/**
 * URL相关的工具函数
 */

import { Game } from "@/types/game";

/**
 * 生成游戏详情页的URL
 * 优先使用game_name构建URL，如果没有则使用ID
 * @param game 游戏对象或游戏名称/ID
 * @returns 游戏详情页URL
 */
export function getGameDetailUrl(game: Game | string): string {
  if (typeof game === 'string') {
    return `/game/${encodeURIComponent(game.toLowerCase().replace(/\s+/g, '-'))}`;
  }
  
  // 如果是游戏对象
  if (game.game_name) {
    return `/game/${encodeURIComponent(game.game_name.toLowerCase().replace(/\s+/g, '-'))}`;
  }
  
  // 如果没有game_name，回退到使用游戏标题
  if (game.title) {
    return `/game/${encodeURIComponent(game.title.toLowerCase().replace(/\s+/g, '-'))}`;
  }
  
  // 最后回退到ID
  return `/game/${game.id}`;
}

/**
 * 格式化URL slug
 * 将文本转换为URL友好的格式
 * @param text 需要格式化的文本
 * @returns 格式化后的slug
 */
export function formatSlug(text: string): string {
  return text.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '');
}

/**
 * 生成分类页面的URL
 * 使用category_pilot作为URL标识符
 * @param category 分类对象
 * @returns 分类页面URL
 */
export function getCategoryUrl(category: { category_pilot: string }): string {
  return `/category/${encodeURIComponent(category.category_pilot.toLowerCase())}`;
} 