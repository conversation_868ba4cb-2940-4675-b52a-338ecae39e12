'use client';

import { useRecentGames } from '@/contexts/RecentGamesContext';
import Link from 'next/link';
import Image from 'next/image';
import { GameSection } from '@/components/ui/game-section';
import { PageHeader } from '@/components/ui/page-header';
import { Game } from '@/types/game';
import { PageLayout } from '@/components/ui/page-layout';
import { getGameDetailUrl } from '@/lib/url-utils';

export default function RecentGamesPage() {
  const { recentGames, clearRecentGames } = useRecentGames();

  return (
    <main className="min-h-screen bg-primary pt-20">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题和清除按钮 */}
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-[#002b50]">Recent Games</h1>
          {recentGames.length > 0 && (
            <button
              onClick={clearRecentGames}
              className="px-4 py-2 bg-white rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-colors text-sm"
            >
              Clear All
            </button>
          )}
        </div>

        {/* 游戏列表 */}
        {recentGames.length === 0 ? (
          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <div className="text-gray-500 mb-4">No recent games yet</div>
            <Link
              href="/"
              className="inline-flex items-center text-[#0095ff] hover:text-[#0084e3] transition-colors"
            >
              <svg viewBox="0 0 24 24" className="w-5 h-5 mr-2" fill="currentColor">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
              </svg>
              Browse Games
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {recentGames.map(game => (
              <Link
                key={game.id}
                href={getGameDetailUrl(game)}
                className="block group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="relative aspect-[4/3]">
                  <Image
                    src={game.image}
                    alt={game.title}
                    fill
                    className="object-cover transition-transform group-hover:scale-110"
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                  />
                  {game.isHot && (
                    <div className="absolute top-2 left-2">
                      <Image
                        src="/images/hot.png"
                        alt="Hot"
                        width={40}
                        height={40}
                      />
                    </div>
                  )}
                  {game.isNew && (
                    <div className="absolute top-2 left-2">
                      <Image
                        src="/images/new.png"
                        alt="New"
                        width={40}
                        height={40}
                      />
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <h3 className="text-sm font-medium text-gray-800 line-clamp-2 group-hover:text-[#0095ff]">
                    {game.title}
                  </h3>
                  {game.category && (
                    <span className="inline-block mt-2 px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                      {game.category}
                    </span>
                  )}
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </main>
  );
} 