import React from 'react';

interface PageContentProps {
  title: string;
  description?: string;
  content?: string;
}

export function PageContent({ title, description, content }: PageContentProps) {
  return (
    <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 mb-6">
      <h1 className="text-3xl font-bold text-[#002b50] mb-3">{title}</h1>
      
      {description && (
        <div className="text-gray-600 mb-6">
          <p>{description}</p>
        </div>
      )}
      
      {content && (
        <div 
          className="prose prose-blue max-w-none"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      )}
    </div>
  );
} 