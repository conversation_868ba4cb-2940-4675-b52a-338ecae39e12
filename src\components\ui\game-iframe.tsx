'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, Maximize, X } from 'lucide-react';
import { Game } from '@/types/game';

interface GameIframeProps {
  game: Game;
  onClose: () => void;
}

export function GameIframe({ game, onClose }: GameIframeProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [controlsVisible, setControlsVisible] = useState(true);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Listen for fullscreen changes
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    // Add iframe-active class to body to prevent touch scrolling
    document.body.classList.add('iframe-active');

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    
    // Auto-hide controls after 3 seconds
    timerRef.current = setTimeout(() => {
      setControlsVisible(false);
    }, 3000);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      // Remove iframe-active class
      document.body.classList.remove('iframe-active');
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, []);

  const toggleFullscreen = async () => {
    try {
      const container = document.getElementById('game-container');
      if (!container) return;

      if (!document.fullscreenElement) {
        await container.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (err) {
      console.error("Failed to toggle fullscreen:", err);
    }
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const handleClose = () => {
    // Ensure iframe-active class is removed when closing
    document.body.classList.remove('iframe-active');
    onClose();
  };
  
  const showControls = () => {
    setControlsVisible(true);
    // Reset auto-hide timer
    if (timerRef.current) clearTimeout(timerRef.current);
    timerRef.current = setTimeout(() => {
      setControlsVisible(false);
    }, 3000);
  };

  return (
    <div 
      id="game-container"
      className={`fixed inset-0 z-50 bg-black ${isFullscreen ? 'fullscreen' : ''}`}
      onMouseMove={showControls}
      onTouchStart={showControls}
    >
      {/* Floating control buttons */}
      <div className={`absolute top-0 left-0 right-0 z-10 transition-opacity duration-300 ${controlsVisible ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex justify-between p-2">
          {/* Left back button */}
          <button
            onClick={handleClose}
            className="p-2 rounded-full bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm transition-colors"
            aria-label="Back"
          >
            <ChevronLeft size={22} />
          </button>
          
          {/* Right function buttons */}
          <div className="flex gap-2">
            <button
              onClick={toggleFullscreen}
              className="p-2 rounded-full bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm transition-colors"
              aria-label="Fullscreen"
            >
              <Maximize size={22} />
            </button>
            <button
              onClick={handleClose}
              className="p-2 rounded-full bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm transition-colors sm:hidden"
              aria-label="Close"
            >
              <X size={22} />
            </button>
          </div>
        </div>
      </div>
      
      {/* Game content */}
      <div className="w-full h-full relative">
        {isLoading && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black text-white z-10">
            <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-blue-500"></div>
            <p className="mt-4">Loading game...</p>
          </div>
        )}
        <iframe
          src={game.url}
          title={game.title}
          className="w-full h-full border-0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          onLoad={handleIframeLoad}
        />
      </div>
    </div>
  );
} 