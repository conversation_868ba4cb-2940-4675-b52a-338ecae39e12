import { Game, GameApiResponse, GameDetailResponse, TagApiResponse } from "@/types/game";

// API Base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://tapixyapi.qilaigames.com";

// Revalidation time (seconds)
const REVALIDATE_TIME = 3600; // 1 hour

/**
 * Convert API game data to application format
 */
function mapApiGameToGame(apiGame: any): Game {
  return {
    id: apiGame.game_id || apiGame.id || '',
    title: apiGame.name || apiGame.title || '',
    image: apiGame.image || '',
    url: apiGame.url || '',
    category: apiGame.category || '',
    tags: apiGame.tags || '',
    description: apiGame.description || '',
    instructions: apiGame.instructions || '',
    isHot: apiGame.is_hot === true || apiGame.isHot === true,
    isNew: apiGame.is_new === true || apiGame.isNew === true,
    isBest: apiGame.is_best === true || apiGame.isBest === true,
    isEditorPick: apiGame.is_editor_pick === true || apiGame.isEditorPick === true,
    game_name: apiGame.game_name || '',
    
    // Metrics
    like_count: apiGame.like_count !== undefined ? apiGame.like_count : 0,
    favorite_count: apiGame.favorite_count !== undefined ? apiGame.favorite_count : 0,
  };
}

/**
 * Convert API game detail data to application format
 */
function mapApiGameDetailToGame(apiGame: GameDetailResponse): Game {
  // 调试输出
  console.log('Input to mapApiGameDetailToGame:', {
    game_id: apiGame.game_id,
    like_count: apiGame.like_count,
    favorite_count: apiGame.favorite_count
  });
  
  const result = {
    id: apiGame.game_id.toString(),
    title: apiGame.name,
    description: apiGame.description,
    image: apiGame.image,
    url: apiGame.file,
    category: apiGame.category_name,
    tags: apiGame.tags ? apiGame.tags.map(tag => tag.name) : [],
    instructions: apiGame.instructions,
    width: apiGame.w.toString(),
    height: apiGame.h.toString(),
    plays: apiGame.plays,
    rating: apiGame.rating,
    featured: apiGame.featured === "1",

    // Additional information
    gameType: apiGame.game_type,
    mobile: apiGame.mobile === 1,
    videoUrl: apiGame.video_url,
    dateAdded: apiGame.date_added,
    game_name: apiGame.game_name,
    
    // Metrics
    like_count: apiGame.like_count !== undefined ? apiGame.like_count : 0,
    favorite_count: apiGame.favorite_count !== undefined ? apiGame.favorite_count : 0,
  };
  
  // 调试输出映射结果
  console.log('Result of mapApiGameDetailToGame:', {
    id: result.id,
    like_count: result.like_count,
    favorite_count: result.favorite_count
  });
  
  return result;
}

/**
 * Get latest game list
 */
export async function fetchLatestGames(page: number = 1, limit: number = 20) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/games/latest?page=${page}&limit=${limit}`,
      {
        next: { revalidate: REVALIDATE_TIME }
      }
    );

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success") {
      throw new Error("Invalid API response format");
    }

    // 处理不同的数据结构
    const gamesData = Array.isArray(result.data) ? result.data :
                     (result.data && Array.isArray(result.data.games) ? result.data.games : []);

    // 映射游戏数据并设置isNew标记
    const games = gamesData.map((game: any) => {
      const mappedGame = mapApiGameToGame(game);
      mappedGame.isNew = true; // 设置最新游戏标记
      return mappedGame;
    });

    return {
      games,
      total: result.data.total || games.length,
      totalPages: result.data.total_pages || Math.ceil(games.length / limit),
      currentPage: result.data.current_page || page
    };
  } catch (error) {
    console.error('Failed to fetch latest games:', error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: page
    };
  }
}

/**
 * Get most popular game list
 */
export async function fetchPopularGames(page: number = 1, limit: number = 20) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/games/popular?page=${page}&limit=${limit}`,
      {
        next: { revalidate: REVALIDATE_TIME }
      }
    );

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success") {
      throw new Error("Invalid API response format");
    }

    // 处理不同的数据结构
    const gamesData = Array.isArray(result.data) ? result.data :
                     (result.data && Array.isArray(result.data.games) ? result.data.games : []);

    // 映射游戏数据并设置isHot标记
    const games = gamesData.map((game: any) => {
      const mappedGame = mapApiGameToGame(game);
      mappedGame.isHot = true; // 设置热门游戏标记
      return mappedGame;
    });

    return {
      games,
      total: result.data.total || games.length,
      totalPages: result.data.total_pages || Math.ceil(games.length / limit),
      currentPage: result.data.current_page || page
    };
  } catch (error) {
    console.error('Failed to fetch popular games:', error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: page
    };
  }
}

/**
 * Get recommended game list
 */
export async function fetchFeaturedGames(page: number = 1, limit: number = 20) {
  try {
    const url = `${API_BASE_URL}/games/featured?page=${page}&limit=${limit}`;

    const response = await fetch(url, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success") {
      throw new Error("Invalid API response format");
    }

    // 处理不同的数据结构
    const gamesData = Array.isArray(result.data) ? result.data :
                     (result.data && Array.isArray(result.data.games) ? result.data.games : []);

    // 映射游戏数据并设置标记
    const games = gamesData.map((game: GameApiResponse) => {
      const mappedGame = mapApiGameToGame(game);
      mappedGame.isEditorPick = true;
      return mappedGame;
    });

    return {
      games,
      total: result.data.total || games.length,
      totalPages: result.data.total_pages || Math.ceil(games.length / limit),
      currentPage: result.data.current_page || page
    };
  } catch (error) {
    console.error("Failed to fetch featured games:", error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: page
    };
  }
}

/**
 * Get top rated game list
 */
export async function fetchTopRatedGames(page: number = 1, limit: number = 20) {
  try {
    const url = `${API_BASE_URL}/games/top-rated?page=${page}&limit=${limit}`;

    const response = await fetch(url, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success") {
      throw new Error("Invalid API response format");
    }

    // 处理不同的数据结构
    const gamesData = Array.isArray(result.data) ? result.data :
                     (result.data && Array.isArray(result.data.games) ? result.data.games : []);

    // 映射游戏数据并设置isBest标记
    const games = gamesData.map((game: GameApiResponse) => {
      const mappedGame = mapApiGameToGame(game);
      mappedGame.isBest = true;
      return mappedGame;
    });

    return {
      games,
      total: result.data.total || games.length,
      totalPages: result.data.total_pages || Math.ceil(games.length / limit),
      currentPage: result.data.current_page || page
    };
  } catch (error) {
    console.error("Failed to fetch top-rated games:", error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: page
    };
  }
}

/**
 * Get game details by ID
 */
export async function fetchGame(id: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/games/${id}`, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      // 任何错误状态码(包括404和500)都返回null，让调用者决定如何处理
      console.warn(`Game API returned status ${response.status} for ID: ${id}`);
      return null;
    }

    const result = await response.json();
    if (result.status !== "success" || !result.data) {
      console.warn(`Invalid API response format for game ID: ${id}`);
      return null;
    }

    return mapApiGameDetailToGame(result.data);
  } catch (error) {
    console.error(`Failed to fetch game ${id}:`, error);
    // 捕获任何其他错误（如网络问题）并返回null
    return null;
  }
}

/**
 * Get game by name (SEO friendly)
 */
export async function fetchGameByName(name: string) {
  try {
    const response = await fetch(`${API_BASE_URL}/games/game/${encodeURIComponent(name)}`, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      // 任何错误状态码(包括404和500)都返回null，让调用者决定如何处理
      console.warn(`Game API returned status ${response.status} for name: ${name}`);
      return null;
    }

    const result = await response.json();
    if (result.status !== "success" || !result.data) {
      console.warn(`Invalid API response format for game name: ${name}`);
      return null;
    }

    // 调试：检查API返回的原始数据
    console.log('API raw data:', {
      game_id: result.data.game_id,
      name: result.data.name,
      like_count: result.data.like_count,
      favorite_count: result.data.favorite_count
    });

    const mappedGame = mapApiGameDetailToGame(result.data);
    
    // 调试：检查映射后的数据
    console.log('Mapped game data:', {
      id: mappedGame.id,
      title: mappedGame.title,
      like_count: mappedGame.like_count,
      favorite_count: mappedGame.favorite_count
    });

    return mappedGame;
  } catch (error) {
    console.error(`Failed to fetch game by name ${name}:`, error);
    // 捕获任何其他错误（如网络问题）并返回null
    return null;
  }
}

/**
 * Get games by category
 */
export async function fetchGamesByCategory(
  categorySlug: string,
  page: number = 1,
  limit: number = 20
): Promise<{
  games: Game[];
  total: number;
  totalPages: number;
  currentPage: number;
}> {
  try {
    // 将连字符转换回空格，恢复原始分类名（如"puzzle-games"转为"Puzzle Games"）
    const originalCategoryName = categorySlug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    // 确保分类名正确编码
    const encodedCategory = encodeURIComponent(originalCategoryName);
    console.log(`Fetching games for category: ${categorySlug}, original name: ${originalCategoryName}, encoded as: ${encodedCategory}, page: ${page}, limit: ${limit}`);

    // 使用category/name路径获取分类游戏，并添加分页参数
    const url = `${API_BASE_URL}/games/category/name/${encodedCategory}?page=${page}&limit=${limit}`;
    console.log(`API URL: ${url}`);

    const response = await fetch(
      url,
      {
        next: { revalidate: REVALIDATE_TIME }
      }
    );

    if (!response.ok) {
      if (response.status === 404 || response.status === 422) {
        console.warn(`Category API endpoint not found for ${categorySlug}. Returning empty array.`);
        return {
          games: [],
          total: 0,
          totalPages: 0,
          currentPage: page
        };
      }
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    console.log(`API response status: ${result.status}`);

    if (result.status !== "success") {
      console.warn(`Invalid API response format for category ${categorySlug}`);
      return {
        games: [],
        total: 0,
        totalPages: 0,
        currentPage: page
      };
    }

    // 处理不同的数据结构
    const gamesData = Array.isArray(result.data) ? result.data :
                     (result.data && Array.isArray(result.data.games) ? result.data.games : []);

    console.log(`Found ${gamesData.length} games for category: ${categorySlug}`);

    // 保持原有的标记
    const games = gamesData.map((game: GameApiResponse) => {
      const mappedGame = mapApiGameToGame(game);

      // 根据类别名称可以添加特定标记
      const lowerCategorySlug = categorySlug.toLowerCase();
      if (lowerCategorySlug.includes('new')) {
        mappedGame.isNew = true;
      }
      if (lowerCategorySlug.includes('hot') || lowerCategorySlug.includes('popular')) {
        mappedGame.isHot = true;
      }
      if (lowerCategorySlug.includes('best') || lowerCategorySlug.includes('top')) {
        mappedGame.isBest = true;
      }

      return mappedGame;
    });

    // 返回带有分页信息的结果
    return {
      games,
      total: result.data.total || result.total || games.length,
      totalPages: result.data.total_pages || Math.ceil((result.data.total || result.total || games.length) / limit),
      currentPage: result.data.current_page || page
    };
  } catch (error) {
    console.error(`Failed to fetch games by category ${categorySlug}:`, error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: page
    };
  }
}

/**
 * Get games by tag ID
 */
export async function fetchGamesByTag(tagId: string, limit = 10): Promise<Game[]> {
  try {
    console.log(`Fetching games for tag ID: ${tagId}, limit: ${limit}`);
    const url = `${API_BASE_URL}/games/tag/${tagId}?limit=${limit}`;

    const response = await fetch(url, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      console.error(`API error: ${response.status} for tag ID: ${tagId}`);
      if (response.status === 404) {
        return [];
      }
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !Array.isArray(result.data)) {
      console.warn(`Invalid API response format for tag ID: ${tagId}`);
      return [];
    }

    // 获取标签信息，用于设置标记
    let tagName = "";
    try {
      const tagResponse = await fetch(`${API_BASE_URL}/tags/${tagId}`, {
        next: { revalidate: REVALIDATE_TIME }
      });

      if (tagResponse.ok) {
        const tagResult = await tagResponse.json();
        if (tagResult.status === "success" && tagResult.data) {
          tagName = tagResult.data.name || "";
        }
      }
    } catch (error) {
      console.error(`Failed to fetch tag info for ID ${tagId}:`, error);
    }

    console.log(`Found ${result.data.length} games for tag ID: ${tagId}, tag name: ${tagName}`);

    // 映射游戏数据并添加适当的标记
    return result.data.map((game: GameApiResponse) => {
      const mapped = mapApiGameToGame(game);

      // 根据标签名称设置标记
      if (tagName.toLowerCase().includes('new')) {
        mapped.isNew = true;
      }
      if (tagName.toLowerCase().includes('hot') || tagName.toLowerCase().includes('popular')) {
        mapped.isHot = true;
      }
      if (tagName.toLowerCase().includes('best') || tagName.toLowerCase().includes('top')) {
        mapped.isBest = true;
      }

      return mapped;
    });
  } catch (error) {
    console.error(`Failed to fetch games by tag ${tagId}:`, error);
    return [];
  }
}

/**
 * Get game list by tag name
 */
export async function fetchGamesByTagName(tagName: string, page: number = 1, limit: number = 20) {
  try {
    console.log(`Fetching games for tag: ${tagName}, page: ${page}, limit: ${limit}`);

    const response = await fetch(
      `${API_BASE_URL}/games/tag/name/${encodeURIComponent(tagName)}?page=${page}&limit=${limit}`,
      {
        next: { revalidate: REVALIDATE_TIME }
      }
    );

    if (!response.ok) {
      console.error(`API error: ${response.status} for tag: ${tagName}`);
      if (response.status === 404) {
        return {
          games: [],
          total: 0,
          totalPages: 0,
          currentPage: 1
        };
      }
      throw new Error(`API error: ${response.status}`);
    }

    const responseData = await response.json();
    console.log('API Response status:', responseData.status);
    console.log('API Response data type:', responseData.data ? (Array.isArray(responseData.data) ? 'array' : 'object') : 'null');

    // 检查返回的数据结构
    if (responseData.status === "success") {
      // 如果是数组直接使用，如果有games属性则使用该属性
      const gamesData = Array.isArray(responseData.data) ? responseData.data :
                        (responseData.data && Array.isArray(responseData.data.games) ? responseData.data.games : []);

      console.log(`Found ${gamesData.length} games for tag: ${tagName}`);

      // 映射游戏数据并添加适当的标记
      const mappedGames = gamesData.map((game: any) => {
        const mapped = mapApiGameToGame(game);

        // 根据标签名称设置标记
        if (tagName.toLowerCase().includes('new')) {
          mapped.isNew = true;
        }
        if (tagName.toLowerCase().includes('hot') || tagName.toLowerCase().includes('popular')) {
          mapped.isHot = true;
        }
        if (tagName.toLowerCase().includes('best') || tagName.toLowerCase().includes('top')) {
          mapped.isBest = true;
        }

        console.log('Mapped game:', game.game_id || game.id, mapped.id);
        return mapped;
      });

      const resultData = {
        games: mappedGames,
        total: responseData.data.total || gamesData.length,
        totalPages: responseData.data.total_pages || Math.ceil(gamesData.length / limit),
        currentPage: responseData.data.current_page || page
      };

      console.log(`Returning ${resultData.games.length} games, total: ${resultData.total}, pages: ${resultData.totalPages}`);
      return resultData;
    }

    // 如果状态不是success，返回空数组
    console.warn(`API returned non-success status: ${responseData.status} for tag: ${tagName}`);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: 1
    };
  } catch (error) {
    console.error(`Failed to fetch games by tag name ${tagName}:`, error);
    return {
      games: [],
      total: 0,
      totalPages: 0,
      currentPage: 1
    };
  }
}

/**
 * Get all categories
 */
export async function fetchAllCategories() {
  try {
    const response = await fetch(`${API_BASE_URL}/categories/all`, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      // 特别处理422错误
      if (response.status === 422) {
        console.warn('Categories API returned 422 Unprocessable Entity. Using default categories.');
        // 返回默认分类
        return [
          { id: 1, name: "Action", url: "action", image: "" },
          { id: 2, name: "Adventure", url: "adventure", image: "" },
          { id: 3, name: "Puzzle", url: "puzzle", image: "" },
          { id: 4, name: "Racing", url: "racing", image: "" },
          { id: 5, name: "Sports", url: "sports", image: "" },
          { id: 6, name: "Strategy", url: "strategy", image: "" },
          { id: 7, name: "Shooting", url: "shooting", image: "" }
        ];
      }
      
      if (response.status === 404) {
        console.warn('Categories API endpoint not found. Returning empty array.');
        return [];
      }
      
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !Array.isArray(result.data)) {
      console.warn('Invalid API response format for categories');
      return [];
    }

    return result.data;
  } catch (error) {
    console.error('Failed to fetch all categories:', error);
    // 出错时返回默认分类列表
    return [
      { id: 1, name: "Action", url: "action", image: "" },
      { id: 2, name: "Adventure", url: "adventure", image: "" },
      { id: 3, name: "Puzzle", url: "puzzle", image: "" },
      { id: 4, name: "Racing", url: "racing", image: "" },
      { id: 5, name: "Sports", url: "sports", image: "" },
      { id: 6, name: "Strategy", url: "strategy", image: "" },
      { id: 7, name: "Shooting", url: "shooting", image: "" }
    ];
  }
}

/**
 * Get home categories for display
 */
export async function fetchHomeCategories() {
  try {
    const response = await fetch(`${API_BASE_URL}/categories/home`, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.warn('Categories API endpoint not found. Returning empty array.');
        return [];
      }
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !Array.isArray(result.data)) {
      console.warn('Invalid API response format for categories');
      return [];
    }

    return result.data;
  } catch (error) {
    console.error('Failed to fetch home categories:', error);
    return [];
  }
}

/**
 * Get all tags
 */
export async function fetchAllTags(): Promise<TagApiResponse[]> {
  try {
    const url = `${API_BASE_URL}/tags/`;

    const response = await fetch(url, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !Array.isArray(result.data)) {
      throw new Error("Invalid API response format");
    }

    return result.data;
  } catch (error) {
    console.error("Failed to fetch all tags:", error);
    return [];
  }
}

/**
 * Get home tags for display
 */
export async function fetchHomeTags() {
  try {
    const response = await fetch(`${API_BASE_URL}/tags/home`, {
      next: { revalidate: REVALIDATE_TIME }
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.warn('Tags API endpoint not found. Returning empty array.');
        return [];
      }
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !Array.isArray(result.data)) {
      console.warn('Invalid API response format for tags');
      return [];
    }

    return result.data;
  } catch (error) {
    console.error('Failed to fetch home tags:', error);
    return [];
  }
}

/**
 * 获取推荐游戏列表 - 基于当前游戏的类别和标签
 *
 * 推荐逻辑：
 * 1. 首先尝试获取相同类别的游戏
 * 2. 如果相同类别的游戏不足，则尝试获取相同标签的游戏
 * 3. 如果仍然不足，则使用热门游戏补充
 * 4. 确保不包含当前正在查看的游戏
 */
export async function fetchRecommendedGames(gameId: string, limit: number = 6) {
  try {
    // 首先获取当前游戏的详细信息，以便获取其类别和标签
    const currentGame = await fetchGame(gameId);

    if (!currentGame) {
      console.warn(`Cannot fetch current game with ID: ${gameId}, falling back to latest games`);
      // 如果无法获取当前游戏信息，则返回最新游戏
      return fetchLatestGamesForRecommendation(gameId, limit);
    }

    // 存储所有推荐游戏的集合
    let recommendedGames: Game[] = [];

    // 1. 首先尝试获取相同类别的游戏
    if (currentGame.category) {
      console.log(`Fetching games from same category: ${currentGame.category}`);
      const categoryGames = await fetchGamesByCategoryForRecommendation(currentGame.category, gameId, limit);
      recommendedGames = [...recommendedGames, ...categoryGames];
    }

    // 2. 如果相同类别的游戏不足，则尝试获取相同标签的游戏
    if (recommendedGames.length < limit && currentGame.tags && currentGame.tags.length > 0) {
      // 只使用前3个标签避免过多请求
      for (const tag of currentGame.tags.slice(0, 3)) {
        if (recommendedGames.length >= limit) break;

        console.log(`Fetching games with tag: ${tag}`);
        const tagGames = await fetchGamesByTagNameForRecommendation(tag, gameId, limit - recommendedGames.length);

        // 添加不重复的游戏
        for (const game of tagGames) {
          if (!recommendedGames.some(g => g.id === game.id)) {
            recommendedGames.push(game);
            if (recommendedGames.length >= limit) break;
          }
        }
      }
    }

    // 3. 如果仍然不足，则使用热门游戏补充
    if (recommendedGames.length < limit) {
      console.log(`Still need ${limit - recommendedGames.length} more games, fetching popular games`);
      const popularGamesData = await fetchPopularGames(1, limit * 2);

      // 添加不重复的游戏
      for (const game of popularGamesData.games) {
        if (game.id !== gameId && !recommendedGames.some(g => g.id === game.id)) {
          recommendedGames.push(game);
          if (recommendedGames.length >= limit) break;
        }
      }
    }

    // 如果仍然不足，使用最新游戏补充
    if (recommendedGames.length < limit) {
      console.log(`Still need ${limit - recommendedGames.length} more games, fetching latest games`);
      const latestGames = await fetchLatestGamesForRecommendation(gameId, limit * 2);

      // 添加不重复的游戏
      for (const game of latestGames) {
        if (!recommendedGames.some(g => g.id === game.id)) {
          recommendedGames.push(game);
          if (recommendedGames.length >= limit) break;
        }
      }
    }

    // 确保不超过请求的数量限制
    return recommendedGames.slice(0, limit);
  } catch (error) {
    console.error(`Failed to fetch recommended games for game ${gameId}:`, error);
    // 出错时返回最新游戏作为备选
    return fetchLatestGamesForRecommendation(gameId, limit);
  }
}

/**
 * 获取最新游戏用于推荐，排除当前游戏
 */
async function fetchLatestGamesForRecommendation(currentGameId: string, limit: number): Promise<Game[]> {
  try {
    const response = await fetch(
      `${API_BASE_URL}/games/latest?limit=${limit * 2}`,
      {
        next: { revalidate: REVALIDATE_TIME }
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return [];
      }
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !Array.isArray(result.data)) {
      throw new Error("Invalid API response format");
    }

    // 映射并过滤掉当前游戏
    const games = result.data
      .map((game: GameApiResponse) => mapApiGameToGame(game))
      .filter((game: Game) => game.id !== currentGameId);

    return games.slice(0, limit);
  } catch (error) {
    console.error(`Failed to fetch latest games for recommendation:`, error);
    return [];
  }
}

/**
 * 获取同类别游戏用于推荐，排除当前游戏
 */
async function fetchGamesByCategoryForRecommendation(category: string, currentGameId: string, limit: number): Promise<Game[]> {
  try {
    const categoryData = await fetchGamesByCategory(category, 1, limit * 2);

    // 过滤掉当前游戏
    return categoryData.games
      .filter(game => game.id !== currentGameId)
      .slice(0, limit);
  } catch (error) {
    console.error(`Failed to fetch category games for recommendation:`, error);
    return [];
  }
}

/**
 * 获取同标签游戏用于推荐，排除当前游戏
 */
async function fetchGamesByTagNameForRecommendation(tagName: string, currentGameId: string, limit: number): Promise<Game[]> {
  try {
    const tagData = await fetchGamesByTagName(tagName, 1, limit * 2);

    // 过滤掉当前游戏
    return tagData.games
      .filter((game: Game) => game.id !== currentGameId)
      .slice(0, limit);
  } catch (error) {
    console.error(`Failed to fetch tag games for recommendation:`, error);
    return [];
  }
}

/**
 * Get page content by url
 */
export async function fetchPageContent(pageUrl: string) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/pages${pageUrl}`,
      {
        next: { revalidate: REVALIDATE_TIME }
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`API error: ${response.status}`);
    }

    const result = await response.json();
    if (result.status !== "success" || !result.data) {
      throw new Error("Invalid API response format");
    }

    return {
      pageName: result.data.page_name || '',
      pageUrl: result.data.page_url || '',
      description: result.data.description || '',
      hasContent: result.data.has_content === "1",
      content: result.data.content_value || ''
    };
  } catch (error) {
    console.error(`Failed to fetch page content for ${pageUrl}:`, error);
    return null;
  }
}

// 分类接口
interface Category {
  id: number;
  name: string;
  image: string;
  show_home: number;
}

interface ApiCategoryResponse {
  status: string;
  data: Category[];
}

// 获取分类列表
export async function getCategories(): Promise<Category[]> {
  try {
    const response = await fetch('https://tapixyapi.qilaigames.com/categories/?skip=0&limit=50');
    const data: ApiCategoryResponse = await response.json();
    
    if (data.status === 'success') {
      return data.data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

// 获取分类游戏列表
export async function fetchCategoryGames(category: string, skip = 0, limit = 12) {
  try {
    // 使用正确的 API 路径格式
    const response = await fetch(
      `${API_BASE_URL}/games/category/name/${encodeURIComponent(category.toLowerCase())}?skip=${skip}&limit=${limit}`,
      { next: { revalidate: 3600 } }
    );

    if (!response.ok) {
      console.error(`Failed to fetch games for category ${category} with status ${response.status}`);
      return {
        games: [],
        total: 0
      };
    }

    const result = await response.json();
    
    if (result.status !== "success") {
      console.error(`Invalid API response format for category ${category}`);
      return {
        games: [],
        total: 0
      };
    }

    // 处理不同的数据结构
    const gamesData = Array.isArray(result.data) ? result.data :
                     (result.data && Array.isArray(result.data.games) ? result.data.games : []);

    return {
      games: gamesData.map(mapApiGameToGame),
      total: result.data.total || gamesData.length
    };
  } catch (error) {
    console.error(`Failed to fetch games for category ${category}:`, error);
    return {
      games: [],
      total: 0
    };
  }
}