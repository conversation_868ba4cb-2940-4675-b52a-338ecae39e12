'use client';

import React, { useEffect, useState } from 'react';

interface PageDescription {
  page_name: string;
  page_url: string;
  description: string;
  has_content: string;
  content_value?: string;
}

const defaultDescription = `
<p>Are you tired of the same old games that offer little excitement? Look no further! Our collection of <strong>crazy games unblocked</strong> is packed with thrilling, action-packed adventures and mind-bending puzzles that will keep you hooked for hours. Whether you're a fan of high-speed racing or intense first-person shooters, we have something for everyone.</p>

<p>Explore our <strong>crazy dress-up games</strong>, where you'll find titles that challenge your creativity and keep you on the edge of your seat. From racing games that test your reflexes to immersive action-packed shooters, there's no shortage of adrenaline-pumping fun. And with our <strong>unblocked versions</strong>, you can enjoy these crazy games anywhere, anytime.</p>

<p>One of the standout categories in our collection is <strong>crazy fashion games</strong>. Get ready to dive into blocky worlds filled with endless possibilities. Build, explore, and survive in lush forests or desolate deserts. With multiplayer options available, you can team up with friends for a unique and unforgettable experience.</p>

<p>But it's not all about the latest trends – we also have a fantastic selection of <strong>crazy games in Spanish</strong>, offering a variety of experiences that will challenge your skills and reflexes. Whether you're playing classic card games like <strong>Crazy Uno</strong> or aiming for victory in archery competitions, there's something for everyone to enjoy.</p>`;

export function PageDescription() {
  const [description, setDescription] = useState<PageDescription | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDescription = async () => {
      try {
        const response = await fetch('https://tapixyapi.qilaigames.com/pages/');
        const data = await response.json();
        if (data.status === 'success') {
          setDescription(data.data);
        }
      } catch (error) {
        console.error('Failed to fetch page description:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDescription();
  }, []);

  if (isLoading) return null;

  return (
    <div className="w-full bg-[#fff8d4] rounded-3xl p-8 space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-[#1a4b8c] mb-2">
          Welcome to TapixyGames
        </h2>
        <h3 className="text-xl font-bold text-[#1a4b8c] mb-4">
          Your Ultimate Free Online Gaming Destination
        </h3>
        <div 
          className="text-[#1a4b8c] leading-relaxed space-y-4"
          dangerouslySetInnerHTML={{ 
            __html: description?.description || defaultDescription 
          }}
        />
      </div>

      <div className="space-y-6">
        <h3 className="text-xl font-bold text-[#1a4b8c]">Why Choose TapixyGames?</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h4 className="font-bold text-[#1a4b8c] text-lg">🎮 Extensive Game Collection</h4>
            <ul className="space-y-2 text-[#1a4b8c]">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Curated library of 1,000+ high-quality games across all genres</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>New games added daily to keep the excitement fresh</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Featured selections handpicked by our gaming experts</span>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-bold text-[#1a4b8c] text-lg">⚡ Instant Gaming Experience</h4>
            <ul className="space-y-2 text-[#1a4b8c]">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>No downloads or installations required - play instantly in your browser</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Optimized for seamless performance across all devices</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Zero registration needed - jump straight into the action</span>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-bold text-[#1a4b8c] text-lg">🌍 Global Gaming Community</h4>
            <ul className="space-y-2 text-[#1a4b8c]">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Available in 20+ languages for players worldwide</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Active community with real-time leaderboards</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Regular events and competitions to engage players</span>
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-bold text-[#1a4b8c] text-lg">🔒 Safe & Fair Gaming</h4>
            <ul className="space-y-2 text-[#1a4b8c]">
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Family-friendly content suitable for all ages</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>No hidden fees or pay-to-win mechanics</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">•</span>
                <span>Regular updates and quality assurance</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="space-y-4 mt-8">
        <h3 className="text-xl font-bold text-[#1a4b8c]">For Game Developers</h3>
        <div className="bg-white/50 rounded-2xl p-6">
          <ul className="space-y-3 text-[#1a4b8c]">
            <li className="flex items-start">
              <span className="mr-2">🚀</span>
              <span>Simple integration process for H5, WebGL, and Unity games</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">💰</span>
              <span>Competitive revenue sharing up to 80%</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">📊</span>
              <span>Comprehensive analytics and performance tracking</span>
            </li>
            <li className="flex items-start">
              <span className="mr-2">🎯</span>
              <span>Global exposure and marketing support</span>
            </li>
          </ul>
        </div>
      </div>

      {description?.has_content === "1" && description?.content_value && (
        <div 
          className="text-[#1a4b8c] mt-8"
          dangerouslySetInnerHTML={{ 
            __html: description.content_value 
          }}
        />
      )}
    </div>
  );
} 