'use client';

import React from 'react';
import { SidebarProvider } from "@/components/ui/sidebar-provider";
import { RandomGameButton } from "@/components/ui/random-game-button";
import ClientLayout from "@/components/layouts/client-layout";
import { BackButtonProvider } from "@/contexts/BackButtonContext";
// import { Header } from '@/components/ui/header';

export default function ClientRootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <BackButtonProvider>
      {/* <Header /> */}
      <ClientLayout>
        <SidebarProvider>
          {children}
          <RandomGameButton />
        </SidebarProvider>
      </ClientLayout>
    </BackButtonProvider>
  );
} 