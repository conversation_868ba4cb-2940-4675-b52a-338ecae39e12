'use client';

import { useState } from 'react';
import { Game } from '@/types/game';
import { useRecentGames } from '@/contexts/RecentGamesContext';
import { trackEvent } from '@/lib/utils';

export function PlayGameButton({ game, onPlay }: { game: Game; onPlay: () => void }) {
  const { addRecentGame } = useRecentGames();

  const handlePlayGame = () => {
    try {
      // 记录到最近游戏列表
      addRecentGame(game);

      // 跟踪游戏播放事件
      trackEvent('Game', 'Play', game.title, game.id);

      // 调用父组件的onPlay回调
      onPlay();
    } catch (error) {
      console.error('Error adding game to recent list:', error);
    }
  };

  return (
    <button
      onClick={handlePlayGame}
      className="relative w-full sm:w-auto min-w-[240px] group"
    >
      {/* 按钮背景 - 添加渐变和阴影效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0095ff] to-[#0065ff] rounded-full opacity-90  group-hover:opacity-100 transition-opacity" />

      {/* 按钮内容 */}
      <div className="relative bg-gradient-to-br from-[#0095ff] to-[#0065ff] text-white px-10 py-4 rounded-full text-lg font-medium shadow-lg flex items-center justify-center gap-3 transform transition-all duration-200 hover:shadow-[0_8px_16px_rgba(0,108,255,0.4)] hover:-translate-y-0.5 active:translate-y-0 active:shadow-[0_4px_8px_rgba(0,108,255,0.4)]">
        <svg viewBox="0 0 24 24" className="w-7 h-7" fill="currentColor">
          <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-10 7H8v3H6v-3H3v-2h3V8h2v3h3v2zm4.5 2c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm4-3c-.83 0-1.5-.67-1.5-1.5S18.67 9 19.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
        </svg>
        Play Now
      </div>
    </button>
  );
}