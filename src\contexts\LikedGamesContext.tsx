'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface ToggleLikeResponse {
  likeCount: number;
  isLiked: boolean;
}

interface LikedGamesContextType {
  likedGames: string[];
  toggleLike: (gameId: string) => Promise<ToggleLikeResponse>;
  isLiked: (gameId: string) => boolean;
}

const LikedGamesContext = createContext<LikedGamesContextType | undefined>(undefined);

const STORAGE_KEY = 'liked_games';
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://tapixyapi.qilaigames.com';

export function LikedGamesProvider({ children }: { children: React.ReactNode }) {
  const [likedGames, setLikedGames] = useState<string[]>([]);

  useEffect(() => {
    // 从本地存储加载点赞的游戏列表
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        setLikedGames(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading liked games:', error);
    }
  }, []);

  const toggleLike = async (gameId: string): Promise<ToggleLikeResponse> => {
    try {
      // 先更新本地状态，让UI立即反应
      const isCurrentlyLiked = likedGames.includes(gameId);
      const action = isCurrentlyLiked ? 'remove' : 'add';

      setLikedGames(prev => {
        const newLikedGames = isCurrentlyLiked
          ? prev.filter(id => id !== gameId)
          : [...prev, gameId];
        
        // 保存到本地存储
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(newLikedGames));
        } catch (error) {
          console.error('Error saving to localStorage:', error);
        }
        
        return newLikedGames;
      });

      // 构建API URL和参数
      const url = new URL(`${API_BASE_URL}/games/toggle-like/${gameId}`);
      url.searchParams.append('action', action);

      // 调用后端 API
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: action
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to toggle like');
      }

      // 返回更新后的点赞数和状态
      return {
        likeCount: data.like_count || 0,
        isLiked: data.is_liked || !isCurrentlyLiked
      };
    } catch (error) {
      console.error('Error toggling like:', error);
      // 返回一个默认值，保持UI一致性
      return {
        likeCount: 0,
        isLiked: !likedGames.includes(gameId)
      };
    }
  };

  const isLiked = (gameId: string) => {
    return likedGames.includes(gameId);
  };

  return (
    <LikedGamesContext.Provider value={{ likedGames, toggleLike, isLiked }}>
      {children}
    </LikedGamesContext.Provider>
  );
}

export function useLikedGames() {
  const context = useContext(LikedGamesContext);
  if (context === undefined) {
    throw new Error('useLikedGames must be used within a LikedGamesProvider');
  }
  return context;
} 