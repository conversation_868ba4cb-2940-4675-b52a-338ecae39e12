'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Search, Loader2 } from 'lucide-react';
import { Game } from '@/types/game';

interface SearchResultsProps {
  initialKeyword: string;
}

const API_BASE_URL = 'https://tapixyapi.qilaigames.com';

export function SearchResults({ initialKeyword }: SearchResultsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [keyword, setKeyword] = useState(initialKeyword);
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const limit = 20; // 增加每页显示游戏数量

  const handleSearch = async (searchKeyword: string, skip = 0) => {
    if (!searchKeyword.trim()) {
      setGames([]);
      setTotal(0);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/games/search?keyword=${encodeURIComponent(searchKeyword)}&skip=${skip}&limit=${limit}`
      );
      
      if (!response.ok) {
        throw new Error('Search request failed');
      }

      const data = await response.json();
      
      if (data.status === 'success') {
        if (skip === 0) {
          setGames(data.data);
        } else {
          setGames(prev => [...prev, ...data.data]);
        }
        setTotal(data.total);
      }
    } catch (error) {
      console.error('Error searching games:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (initialKeyword) {
      setKeyword(initialKeyword);
      handleSearch(initialKeyword);
    }
  }, [initialKeyword]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (keyword.trim()) {
      router.push(`/search?keyword=${encodeURIComponent(keyword)}`);
      handleSearch(keyword);
      setPage(1);
    }
  };

  const loadMore = () => {
    const skip = page * limit;
    handleSearch(keyword, skip);
    setPage(prev => prev + 1);
  };

  const hasMore = games.length < total;

  return (
    <div className="max-w-7xl mx-auto">
      {/* 搜索框 */}
      <div className="mb-8 bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-md">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <div className="relative flex-1">
            <input
              type="text"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              placeholder="Search games..."
              className="w-full px-4 py-3 pl-12 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-[#002b50] focus:border-transparent shadow-sm"
            />
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          </div>
          <button
            type="submit"
            className="px-8 py-3 bg-[#002b50] text-white rounded-full hover:bg-[#003b6d] transition-colors shadow-md font-medium"
          >
            Search
          </button>
        </form>
      </div>

      {/* 搜索结果 */}
      {loading && games.length === 0 ? (
        <div className="text-center py-12 bg-white/60 backdrop-blur-sm rounded-2xl shadow-md">
          <Loader2 className="animate-spin h-12 w-12 mx-auto text-[#002b50]" />
          <p className="mt-4 text-[#002b50] font-medium">Searching for games...</p>
        </div>
      ) : games.length > 0 ? (
        <>
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-md mb-6">
            <h2 className="text-xl font-bold text-[#002b50]">
              Found {total} game{total !== 1 ? 's' : ''} for "{keyword}"
            </h2>
          </div>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 md:gap-5">
            {games.map((game, index) => (
              <div key={`game-${game.id || index}`} className="w-full aspect-[4/3] max-w-[180px] mx-auto">
                <Link
                  href={`/game/${game.game_name}`}
                  className="group block bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-200 h-full transform hover:scale-105"
                >
                  <div className="aspect-[4/3] relative">
                    <Image
                      src={game.image || ''}
                      alt={`${game.title || 'Game image'}`}
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
                    />
                    {game.isHot && (
                      <span className="absolute top-2 left-2 bg-red-500 text-white text-xs sm:text-xs px-1 py-0.5 sm:px-2 sm:py-1 rounded-full font-medium shadow-sm text-[8px] sm:text-xs">
                        HOT
                      </span>
                    )}
                    {game.isNew && (
                      <span className="absolute top-2 right-2 bg-green-500 text-white text-xs sm:text-xs px-1 py-0.5 sm:px-2 sm:py-1 rounded-full font-medium shadow-sm text-[8px] sm:text-xs">
                        NEW
                      </span>
                    )}
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium text-[#002b50] group-hover:text-[#0095ff] transition-colors line-clamp-1">
                      {game.title}
                    </h3>
                    {game.category && (
                      <p className="text-xs text-gray-500 mt-1 line-clamp-1">{game.category}</p>
                    )}
                  </div>
                </Link>
              </div>
            ))}
          </div>

          {/* 加载更多按钮 */}
          {hasMore && (
            <div className="text-center mt-12 mb-8">
              <button
                onClick={loadMore}
                disabled={loading}
                className="px-10 py-3 bg-white text-[#002b50] rounded-full border border-[#002b50] hover:bg-[#002b50] hover:text-white transition-colors shadow-md disabled:opacity-50 font-medium min-w-[200px]"
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <Loader2 className="animate-spin mr-2 h-4 w-4" />
                    Loading...
                  </span>
                ) : (
                  'Load More Games'
                )}
              </button>
            </div>
          )}
        </>
      ) : keyword && !loading ? (
        <div className="text-center py-16 bg-white/70 backdrop-blur-sm rounded-2xl shadow-md">
          <div className="mx-auto w-24 h-24 bg-yellow-50 rounded-full flex items-center justify-center mb-6">
            <Search className="w-12 h-12 text-gray-400" />
          </div>
          <h2 className="text-xl font-bold text-[#002b50] mb-2">No Games Found</h2>
          <p className="text-gray-600 mb-1">We couldn't find any games matching "{keyword}"</p>
          <p className="text-gray-500 mt-2 max-w-md mx-auto">Try using different keywords or check out our categories for popular games</p>
          <Link 
            href="/"
            className="inline-flex items-center justify-center mt-6 px-6 py-3 bg-gradient-to-r from-[#0095ff] to-[#0065ff] text-white font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200 min-w-[200px] hover:scale-105 active:scale-100"
          >
            Browse Categories
          </Link>
        </div>
      ) : null}
    </div>
  );
} 