CREATE TABLE `gm_games` (
  `game_id` int(11) NOT NULL AUTO_INCREMENT,
  `catalog_id` varchar(250) NOT NULL,
  `game_name` varchar(250) NOT NULL,
  `name` varchar(250) NOT NULL,
  `image` varchar(500) NOT NULL,
  `import` enum('0','1') NOT NULL DEFAULT '0',
  `category` int(11) NOT NULL,
  `plays` int(11) NOT NULL,
  `rating` enum('0','0.5','1','1.5','2','2.5','3','3.5','4','4.5','5') NOT NULL DEFAULT '0',
  `description` varchar(15000) NOT NULL,
  `instructions` varchar(600) NOT NULL,
  `file` varchar(500) NOT NULL,
  `game_type` varchar(250) NOT NULL,
  `w` int(10) NOT NULL,
  `h` int(10) NOT NULL,
  `date_added` int(11) NOT NULL,
  `published` enum('0','1') NOT NULL,
  `featured` enum('0','1') NOT NULL DEFAULT '0',
  `mobile` int(255) NOT NULL,
  `featured_sorting` varchar(255) NOT NULL,
  `field_1` varchar(500) NOT NULL,
  `field_2` varchar(500) NOT NULL,
  `field_3` varchar(500) NOT NULL,
  `field_4` varchar(500) NOT NULL,
  `field_5` varchar(500) NOT NULL,
  `field_6` varchar(500) NOT NULL,
  `field_7` varchar(500) NOT NULL,
  `field_8` varchar(500) NOT NULL,
  `field_9` varchar(500) NOT NULL,
  `field_10` varchar(500) NOT NULL,
  `tags_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin,
  `video_url` varchar(100) DEFAULT NULL,
  `is_last_rewrite` tinyint(1) NOT NULL DEFAULT '0',
  `like_count` int(11) DEFAULT '0',
  `favorite_count` int(11) DEFAULT '0',
  PRIMARY KEY (`game_id`),
  KEY `game_name` (`game_name`),
  KEY `name` (`name`),
  KEY `category` (`category`),
  FULLTEXT KEY `idx_game_search` (`game_name`,`name`,`description`)
) ENGINE=InnoDB AUTO_INCREMENT=25038 DEFAULT CHARSET=latin1;


CREATE TABLE `gm_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_pilot` varchar(250) CHARACTER SET utf8 NOT NULL,
  `name` text COLLATE utf8_unicode_ci NOT NULL,
  `image` varchar(400) COLLATE utf8_unicode_ci NOT NULL,
  `footer_description` text COLLATE utf8_unicode_ci,
  `show_home` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE `gm_tags` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `url` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `footer_description` text COLLATE utf8mb4_unicode_ci,
  `is_last_rewrite` tinyint(1) NOT NULL DEFAULT '0',
  `is_rewrited` tinyint(1) NOT NULL DEFAULT '0',
  `show_home` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `url` (`url`)
) ENGINE=InnoDB AUTO_INCREMENT=465 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;