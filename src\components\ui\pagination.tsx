import React from 'react';
import Link from 'next/link';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  basePath: string;
  hasMore?: boolean;
}

export function Pagination({ currentPage, totalPages, basePath, hasMore = true }: PaginationProps) {
  return (
    <div className="flex items-center justify-center space-x-4">
      {/* Previous page button */}
      {currentPage > 1 ? (
        <Link
          href={`${basePath}?page=${currentPage - 1}`}
          className="flex items-center justify-center px-5 py-3 rounded-xl bg-white shadow-md hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 transform hover:scale-105 group"
        >
          <ChevronLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
          <span className="font-medium">Previous</span>
        </Link>
      ) : (
        <span className="flex items-center justify-center px-5 py-3 rounded-xl bg-gray-100 text-gray-400 opacity-50 cursor-not-allowed">
          <ChevronLeft className="w-5 h-5 mr-2" />
          <span className="font-medium">Previous</span>
        </span>
      )}

      {/* Next page button - 使用hasMore参数或强制总是显示"下一页"按钮 */}
      {hasMore ? (
        <Link
          href={`${basePath}?page=${currentPage + 1}`}
          className="flex items-center justify-center px-5 py-3 rounded-xl bg-white shadow-md hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 transform hover:scale-105 group"
        >
          <span className="font-medium">Next</span>
          <ChevronRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
        </Link>
      ) : (
        <span className="flex items-center justify-center px-5 py-3 rounded-xl bg-gray-100 text-gray-400 opacity-50 cursor-not-allowed">
          <span className="font-medium">Next</span>
          <ChevronRight className="w-5 h-5 ml-2" />
        </span>
      )}
    </div>
  );
} 