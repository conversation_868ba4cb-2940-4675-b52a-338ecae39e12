import React from 'react';

interface PageHeaderProps {
  title: string;
  description?: string;
}

export function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="text-center">
      <h1 className="text-2xl sm:text-3xl font-bold text-[#002b50] mb-2">
        {title}
      </h1>
      {description && (
        <p className="text-gray-600 text-sm sm:text-base max-w-3xl mx-auto">
          {description}
        </p>
      )}
    </div>
  );
} 