/**
 * 处理游戏图片加载问题
 * 通过适当的方式处理图片URL，避免优化超时问题
 */

// 需要使用unoptimized属性的域名
const UNOPTIMIZED_DOMAINS = [
  'playgame.qilaigames.com'
];

/**
 * 检查图片URL是否需要跳过优化
 * @param imageUrl 图片URL
 * @returns 是否需要跳过Next.js图片优化
 */
export function shouldSkipImageOptimization(imageUrl: string): boolean {
  if (!imageUrl) return false;
  
  try {
    const url = new URL(imageUrl);
    return UNOPTIMIZED_DOMAINS.some(domain => url.hostname.includes(domain));
  } catch (e) {
    // 无效URL，不跳过优化
    return false;
  }
}

/**
 * 返回处理过的图片URL
 * 如果是无效URL或空字符串，返回默认图片
 * @param imageUrl 原始图片URL
 * @returns 处理后的图片URL
 */
export function getSafeImageUrl(imageUrl: string): string {
  if (!imageUrl) {
    return '/images/hot.png'; // 使用现有图片作为默认图
  }
  
  return imageUrl;
} 