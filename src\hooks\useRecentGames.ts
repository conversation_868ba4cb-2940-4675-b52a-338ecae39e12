import { useState, useEffect } from 'react';
import { Game } from '@/types/game';

const RECENT_GAMES_KEY = 'recent_games';
const MAX_RECENT_GAMES = 10;

export function useRecentGames() {
  const [recentGames, setRecentGames] = useState<Game[]>([]);

  // 加载最近游戏
  useEffect(() => {
    const stored = localStorage.getItem(RECENT_GAMES_KEY);
    if (stored) {
      try {
        const games = JSON.parse(stored);
        setRecentGames(games);
      } catch (error) {
        console.error('Failed to parse recent games:', error);
      }
    }
  }, []);

  // 添加游戏到最近列表
  const addRecentGame = (game: Game) => {
    setRecentGames(prev => {
      // 移除重复的游戏
      const filtered = prev.filter(g => g.id !== game.id);
      // 添加到开头
      const updated = [game, ...filtered].slice(0, MAX_RECENT_GAMES);
      // 保存到本地存储
      localStorage.setItem(RECENT_GAMES_KEY, JSON.stringify(updated));
      return updated;
    });
  };

  // 清除最近游戏列表
  const clearRecentGames = () => {
    localStorage.removeItem(RECENT_GAMES_KEY);
    setRecentGames([]);
  };

  return {
    recentGames,
    addRecentGame,
    clearRecentGames
  };
} 