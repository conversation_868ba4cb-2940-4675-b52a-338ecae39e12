import { notFound } from "next/navigation";
// import { Header } from "@/components/ui/header";
import { Sidebar } from "@/components/ui/sidebar";
import { GameCard } from "@/components/ui/game-card";
import { RandomGameButton } from "@/components/ui/random-game-button";
import { fetchGamesByTagName } from "@/lib/api";
import { Game } from "@/types/game";
import Link from "next/link";
import { BackButton } from "@/components/ui/back-button";

// Revalidate the page every 1 hour (3600 seconds)
export const revalidate = 3600;

export default async function TagGamesPage({ params }: { params: { tag_name: string } }) {
  let games: Game[] = [];
  const decodedTagName = decodeURIComponent(params.tag_name);
  
  try {
    // fetchGamesByTagName可能返回带有games属性的对象而不是直接的游戏数组
    const gamesData = await fetchGamesByTagName(decodedTagName);
    
    // 处理不同的返回数据结构
    if (Array.isArray(gamesData)) {
      games = gamesData;
    } else if (gamesData && Array.isArray(gamesData.games)) {
      games = gamesData.games;
    }
  } catch (error) {
    console.error('Failed to fetch games by tag:', error);
  }

  // If no games found, return 404
  if (!games || games.length === 0) {
    notFound();
  }

  return (
    <main className="min-h-screen relative bg-primary">
      <Sidebar />
      <BackButton href="/" />

      <div className="ml-16">
        {/* <Header /> */}

        <div className="px-4 sm:px-8 py-6 max-w-6xl mx-auto pt-[72px]">
          {/* 页面标题 */}
          <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 mb-8 text-center">
            <h1 className="text-2xl sm:text-3xl font-bold text-[#002b50] mb-2">
              {decodedTagName.charAt(0).toUpperCase() + decodedTagName.slice(1)} Games
            </h1>
            <p className="text-gray-600">
              Explore {games.length} games tagged with {decodedTagName}
            </p>
          </div>

          {/* 游戏网格 */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {games.map((game) => (
              <GameCard
                key={game.id}
                imageUrl={game.image}
                title={game.title}
                href={`/game/${game.id}`}
                isHot={game.isHot}
                isNew={game.isNew}
                isBest={game.isBest}
                isEditorPick={game.isEditorPick}
              />
            ))}
          </div>
        </div>
      </div>

      <RandomGameButton />
    </main>
  );
} 