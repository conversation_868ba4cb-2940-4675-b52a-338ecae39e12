---
description:
globs:
alwaysApply: false
---
# 项目结构

本项目遵循标准的Next.js文件结构，主要代码位于`src`目录下。

## 主要目录结构
- [`src/app`](mdc:src/app): Next.js App Router路由和页面
  - [`src/app/layout.tsx`](mdc:src/app/layout.tsx): 根布局
  - [`src/app/page.tsx`](mdc:src/app/page.tsx): 首页
  - [`src/app/game/[slug]/page.tsx`](mdc:src/app/game/[slug]/page.tsx): 游戏详情页
  - 其他路由目录: `categories`, `search`, `recent`, `favorites`等

- [`src/components`](mdc:src/components): 共享组件
  - [`src/components/ui`](mdc:src/components/ui): UI组件(游戏卡片、页面布局等)
  - [`src/components/layouts`](mdc:src/components/layouts): 布局组件

- [`src/contexts`](mdc:src/contexts): React上下文
  - 游戏收藏、最近游戏等状态管理

- [`src/types`](mdc:src/types): TypeScript类型定义
  - [`src/types/game.ts`](mdc:src/types/game.ts): 游戏类型定义

- [`src/data`](mdc:src/data): 静态数据
  - [`src/data/games.ts`](mdc:src/data/games.ts): 游戏数据

- [`src/lib`](mdc:src/lib): 工具函数和API
  - [`src/lib/api.ts`](mdc:src/lib/api.ts): API请求函数

- [`src/hooks`](mdc:src/hooks): 自定义React钩子

- [`src/styles`](mdc:src/styles): 全局样式
