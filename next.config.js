/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  eslint: {
    // we use biome for linting
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.www.tapixy.com',
      },
      {
        protocol: 'https',
        hostname: '**.flycdn.vip',
      },
      {
        protocol: 'https',
        hostname: 'ext.same-assets.com',
      },
      {
        protocol: 'https',
        hostname: 'ugc.same-assets.com',
      },
      {
        protocol: 'https',
        hostname: 'img.gamemonetize.com',
      },
      {
        protocol: 'https',
        hostname: 'html5.gamemonetize.com',
      },
      {
        protocol: 'https',
        hostname: 'playgame.qilaigames.com',
      }
    ],
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  // Enable standalone output for production builds
  output: 'standalone',
  devIndicators: false,
  // 处理API错误
  onDemandEntries: {
    // 页面保持"活跃"状态的时间（毫秒）
    maxInactiveAge: 25 * 1000,
    // 同时保持活动的页面数
    pagesBufferLength: 5,
  },
  // 处理导入Html组件错误
  experimental: {
    serverActions: {
      allowedOrigins: [
        'localhost:3000',
        'www.tapixy.com'
      ],
    },
  },
};

module.exports = nextConfig;
