import { NextResponse } from 'next/server';

// 获取所有分类数据
async function fetchAllCategories() {
  try {
    // 根据实际API调整
    const response = await fetch('https://tapixyapi.qilaigames.com/api/categories');
    const data = await response.json();
    return data.categories || [];
  } catch (error) {
    console.error('Error fetching categories for sitemap:', error);
    return [];
  }
}

export async function GET() {
  const categories = await fetchAllCategories();
  
  // 常见的游戏分类（如果API暂时无法获取）
  const defaultCategories = [
    'action', 'adventure', 'puzzle', 'racing', 'shooting',
    'sports', 'strategy', 'arcade', 'multiplayer', 'kids'
  ];
  
  // 如果API没有返回分类，使用默认分类
  const categoriesToUse = categories.length > 0 
    ? categories 
    : defaultCategories.map(slug => ({ slug }));
  
  // 生成XML
  const xml = `<?xml version="1.0" encoding="UTF-8"?>
  <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${categoriesToUse.map((category: any) => `
      <url>
        <loc>https://www.tapixy.com/category/${category.slug}</loc>
        <lastmod>${new Date().toISOString()}</lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.7</priority>
      </url>
    `).join('')}
  </urlset>`;
  
  // 返回XML响应
  return new NextResponse(xml, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}

// 对于动态内容，不使用缓存
export const dynamic = 'force-dynamic'; 