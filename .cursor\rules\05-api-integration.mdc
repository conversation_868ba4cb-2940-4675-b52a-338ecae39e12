---
description:
globs:
alwaysApply: false
---
# API集成

本项目通过API与后端服务通信，主要API相关代码位于[`src/lib/api.ts`](mdc:src/lib/api.ts)。

## API基本信息

- 测试环境API基础URL: `https://tapixyapi.qilaigames.com`
- 生产环境使用相对路径或配置的API基础URL

## 主要API函数

API函数主要用于获取以下数据：

1. 游戏列表数据
   - 热门游戏
   - 最新游戏
   - 分类游戏
   - 搜索游戏

2. 游戏详情数据
   - 单个游戏详情
   - 相关游戏推荐

3. 分类数据
   - 游戏分类列表
   - 分类详情

4. 标签数据
   - 游戏标签列表
   - 标签详情

## API响应数据结构

API响应数据结构定义在[`src/types/game.ts`](mdc:src/types/game.ts)中：

- `GameApiResponse`: 游戏列表API响应
- `GameDetailResponse`: 游戏详情API响应
- `CategoryApiResponse`: 分类API响应
- `TagApiResponse`: 标签API响应

## API集成规范

1. 不要随意修改现有API接口
2. 新API集成应遵循现有的调用模式和错误处理方式
3. 确保API请求有适当的加载状态和错误处理
4. 尽量使用现有的API函数，避免重复代码
