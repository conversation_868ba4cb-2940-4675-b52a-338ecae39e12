import { Metadata } from 'next';
// import { Header } from "@/components/ui/header";
import { GameSection } from "@/components/ui/game-section";
import { ClientWrapper } from "@/components/client-wrapper";
import {
  fetchLatestGames,
  fetchPopularGames,
  fetchCategoryGames,
  getCategories
} from "@/lib/api";
import { PageDescription } from "@/components/ui/page-description";
import { Footer } from "@/components/ui/footer";
import Link from "next/link";

// Revalidate the page every 1 hour (3600 seconds)
export const revalidate = 3600;

// Page metadata
export const metadata: Metadata = {
  title: 'Play Free Online Games | TapixyGames',
  description: 'Play thousands of free online games on TapixyGames! We have a huge collection of action, puzzle, racing, shooting, and many more fun games.',
  keywords: 'free games, online games, browser games, no download, puzzle games, action games, racing games, strategy games, mobile games, instant play, HTML5 games, free online gaming',
  openGraph: {
    title: 'Play Free Online Games | TapixyGames',
    description: 'Play thousands of free online games on TapixyGames! We have a huge collection of action, puzzle, racing, shooting, and many more fun games.',
  },
};

// Async function to support API data fetching
export default async function Home() {
  try {
    // 获取游戏数据，先并行获取基本游戏数据
    const [latestGames, popularGames, categories] = await Promise.all([
      fetchLatestGames().catch(error => {
        console.error('Failed to fetch latest games:', error);
        return { games: [] };
      }),
      fetchPopularGames().catch(error => {
        console.error('Failed to fetch popular games:', error);
        return { games: [] };
      }),
      getCategories().catch(error => {
        console.error('Failed to fetch categories:', error);
        return [];
      })
    ]);

    // 获取每个分类的游戏
    const categoryGamesPromises = categories
      .filter(cat => cat.show_home === 1) // 只获取需要在首页显示的分类
      .map(category => 
        fetchCategoryGames(category.name, 0, 13).catch(error => {
          console.error(`Failed to fetch games for category ${category.name}:`, error);
          return { games: [] };
        })
      );

    const categoryGamesResults = await Promise.all(categoryGamesPromises);

    // 检查返回的游戏数据
    const latestGamesArray = latestGames.games || [];
    const popularGamesArray = popularGames.games || [];

    return (
      <main className="min-h-screen bg-primary">
        {/* <Header /> */}

        {/* Client wrapper handles sidebar state and interactions */}
        <ClientWrapper>
          <div className="pl-2 pr-8 sm:px-8 pb-16">
            <div className="mt-10" id="new-games">
              <GameSection
                title="New Games"
                games={latestGamesArray}
                viewMoreHref="/games/latest"
              />
            </div>

            <div className="mt-10" id="hot-games">
              <GameSection
                title="Hot Games"
                games={popularGamesArray}
                viewMoreHref="/games/popular"
              />
            </div>

            {/* 分类游戏列表 */}
            {categories
              .filter(cat => cat.show_home === 1)
              .map((category, index) => (
                <div 
                  key={category.id} 
                  className="mt-10"
                  id={category.name.toLowerCase().replace(/\s+/g, '-')}
                >
                  <GameSection
                    title={category.name}
                    games={categoryGamesResults[index].games || []}
                    viewMoreHref={`/category/${category.name.toLowerCase().replace(/\s+/g, '-')}`}
                  />
                </div>
              ))
            }
          </div>

          {/* 页面底部描述 */}
          <div className="px-4 sm:px-8">
            <PageDescription />
          </div>

          {/* 页面底部 */}
          <Footer />
        </ClientWrapper>
      </main>
    );
  } catch (error) {
    console.error('Error in Home page:', error);
    return (
      <div className="min-h-screen bg-primary flex items-center justify-center">
        <div className="bg-white p-8 rounded-2xl shadow-lg">
          <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
          <p className="text-gray-600">Failed to load games. Please try again later.</p>
        </div>
      </div>
    );
  }
}
