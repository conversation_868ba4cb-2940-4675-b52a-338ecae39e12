import { Metadata } from 'next';
import { GameSection } from '@/components/ui/game-section';
import { PageHeader } from '@/components/ui/page-header';
import { fetchPopularGames } from '@/lib/api';
import { Pagination } from '@/components/ui/pagination';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { BackButton } from '@/components/ui/back-button';
import { Game } from '@/types/game';
import { getGameDetailUrl } from '@/lib/url-utils';

export const dynamic = 'force-dynamic';

interface PopularGamesPageProps {
  searchParams: {
    page?: string;
  };
}

export const metadata: Metadata = {
  title: 'Popular Games - Play Free Online Games | TapixyGames',
  description: 'Play the most popular free online games on TapixyGames. These games are loved by thousands of players!',
  openGraph: {
    title: 'Popular Games - TapixyGames',
    description: 'Play the most popular free online games on TapixyGames. These games are loved by thousands of players!',
  },
};

export default async function PopularGamesPage({ searchParams }: PopularGamesPageProps) {
  try {
    const page = searchParams?.page ? parseInt(searchParams.page) : 1;
    
    // 获取热门游戏列表
    const gamesData = await fetchPopularGames(page, 24);
    
    // 检查游戏数据，必须有非空的games数组
    if (!gamesData.games || gamesData.games.length === 0) {
      if (page === 1) {
        // 如果第一页没有游戏，返回404
        return notFound();
      }
    }

    // 页面显示逻辑
    return (
      <main className="min-h-screen bg-primary">
        {/* 使用BackButton组件代替Link+ChevronLeft */}
        <BackButton href="/" />

        <div className="pt-20 sm:pt-24 pb-10 px-4 sm:px-6 max-w-7xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 mb-6">
            <PageHeader
              title="Popular Games"
              description="Play the most popular free online games on TapixyGames. These games are loved by thousands of players!"
            />
          </div>

          {gamesData.games && gamesData.games.length > 0 ? (
            <>
              <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 mb-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-[#002b50]">Popular Games</h2>
                </div>

                <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
                  {gamesData.games.map((game: Game) => (
                    <Link
                      key={game.id}
                      href={getGameDetailUrl(game)}
                      className="block game-card rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="aspect-[4/3] relative">
                        <img 
                          src={game.image} 
                          alt={game.title}
                          className="object-cover w-full h-full"
                        />
                        {game.isHot && (
                          <span className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                            HOT
                          </span>
                        )}
                        {game.isBest && (
                          <span className="absolute top-2 right-2 bg-amber-500 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                            BEST
                          </span>
                        )}
                      </div>
                      <div className="p-2">
                        <h3 className="font-medium text-gray-900 line-clamp-1 text-sm">{game.title}</h3>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
              
              {/* Always show pagination controls regardless of total pages */}
              <div className="flex justify-center mt-8 mb-4">
                <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg p-4 sm:px-8 sm:py-6">
                  <Pagination 
                    currentPage={gamesData.currentPage || 1}
                    totalPages={gamesData.totalPages || 5}
                    basePath="/games/popular"
                    hasMore={true}
                  />
                </div>
              </div>
            </>
          ) : (
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4">
                <svg viewBox="0 0 24 24" className="w-full h-full text-[#002b50]" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-[#002b50] mb-2">No Games Found</h3>
              <p className="text-gray-600 mb-6">There are no popular games available at the moment.</p>
              <Link 
                href="/"
                className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-[#0095ff] to-[#0065ff] text-white font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200 min-w-[200px] hover:scale-105 active:scale-100"
              >
                Back to Home
              </Link>
            </div>
          )}
        </div>
      </main>
    );
  } catch (error) {
    console.error('Error loading popular games:', error);
    return notFound();
  }
} 