'use client';

import React, { createContext, useState, useContext, ReactNode } from 'react';

// 创建侧边栏上下文
interface SidebarContextType {
  isCollapsed: boolean;
  toggleSidebar: () => void;
  setSidebarState: (state: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType>({
  isCollapsed: false,
  toggleSidebar: () => {},
  setSidebarState: () => {},
});

// 自定义Hook用于访问侧边栏上下文
export const useSidebar = () => useContext(SidebarContext);

// 侧边栏提供者组件
export function SidebarProvider({ children }: { children: ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const setSidebarState = (state: boolean) => {
    setIsCollapsed(state);
  };

  return (
    <SidebarContext.Provider value={{ isCollapsed, toggleSidebar, setSidebarState }}>
      {children}
    </SidebarContext.Provider>
  );
} 