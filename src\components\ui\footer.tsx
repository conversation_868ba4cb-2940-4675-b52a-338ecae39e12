'use client';

import React from 'react';
import Link from 'next/link';

export function Footer() {
  return (
    <footer className="text-center py-4 space-y-3 pb-20">
      <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-4 text-[#1a4b8c] px-2">
        <Link href="/about" className="text-sm sm:text-base hover:underline">About</Link>
        <span className="hidden sm:inline text-[#1a4b8c]">•</span>
        <Link href="/contact" className="text-sm sm:text-base hover:underline">Contact</Link>
        <span className="hidden sm:inline text-[#1a4b8c]">•</span>
        <Link href="/privacy" className="text-sm sm:text-base hover:underline">Privacy</Link>
        <span className="hidden sm:inline text-[#1a4b8c]">•</span>
        <Link href="/terms" className="text-sm sm:text-base hover:underline">Terms</Link>
        {/* <Link href="/blogs" className="hover:underline">Blogs</Link> */}
      </div>
      <div className="flex flex-wrap items-center justify-center gap-1 text-[#1a4b8c] text-sm sm:text-base px-2">
        <span className="font-bold">TAPIXY</span>
        <span>© TAPIXY {new Date().getFullYear()} www.tapixy.com</span>
      </div>
    </footer>
  );
} 