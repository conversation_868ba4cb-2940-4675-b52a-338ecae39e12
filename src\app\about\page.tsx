import { Metadata } from 'next';
import { PageLayout } from '@/components/ui/page-layout';
import { PageContent } from '@/components/ui/page-content';
import { fetchPageContent } from '@/lib/api';

export const metadata: Metadata = {
  title: 'About Us - TapixyGames',
  description: 'Learn about TapixyGames, who we are, and our mission to provide the best free online gaming experience.',
  openGraph: {
    title: 'About Us - TapixyGames',
    description: 'Learn about TapixyGames, who we are, and our mission to provide the best free online gaming experience.',
  },
};

export default async function AboutPage() {
  // 尝试从API获取页面内容
  const pageData = await fetchPageContent('/about');
  
  // 定义默认内容，当API请求失败时使用
  const defaultDescription = "TapixyGames is a free online gaming platform dedicated to providing the best gaming experience for players of all ages. We offer a diverse collection of high-quality games that you can play instantly without downloads.";
  const defaultContent = `
    <p>Founded in 2023, TapixyGames has quickly become a popular destination for gamers looking for quick, fun, and engaging gaming experiences. Our mission is to make gaming accessible to everyone, anywhere, and on any device.</p>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Our Games</h2>
    <p>We carefully curate our game selection to ensure we offer something for everyone. From casual puzzles to action-packed adventures, our library is constantly growing with new titles added regularly.</p>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Our Team</h2>
    <p>Our dedicated team of developers and gaming enthusiasts work tirelessly to bring you the best online gaming experience possible. We're passionate about games and committed to creating a platform that gamers love.</p>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Contact Us</h2>
    <p>Have questions or feedback? We'd love to hear from you! Visit our <a href="/contact" class="text-blue-500 hover:underline">Contact page</a> to get in touch with our team.</p>
  `;

  return (
    <PageLayout>
      <PageContent
        title="About Us"
        description={pageData?.description || defaultDescription}
        content={pageData?.hasContent ? pageData.content : defaultContent}
      />
    </PageLayout>
  );
} 