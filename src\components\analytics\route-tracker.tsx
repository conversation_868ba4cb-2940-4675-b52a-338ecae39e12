'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { trackPageView } from '@/lib/utils';

/**
 * 路由跟踪组件 - 监听路由变化并自动发送百度统计
 */
export function RouteTracker() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // 构建完整的URL
    const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '');
    
    // 延迟一小段时间确保页面完全加载
    const timer = setTimeout(() => {
      trackPageView(url);
    }, 100);

    return () => clearTimeout(timer);
  }, [pathname, searchParams]);

  // 这个组件不渲染任何内容
  return null;
}
