'use client';

import React, { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { trackEvent } from '@/lib/utils';
import { Dice5 } from 'lucide-react';

export function RandomGameButton() {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);

  // 如果当前页面是游戏详情页，不显示按钮
  if (pathname?.startsWith('/game/')) {
    return null;
  }

  const handleRandomGame = async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);
      // 跟踪随机游戏点击事件
      trackEvent('Navigation', 'RandomGame', 'ButtonClick');

      // 从最新游戏中获取随机游戏
      const response = await fetch('https://tapixyapi.qilaigames.com/games/random?source=latest');
      const data = await response.json();

      if (data.status === 'success' && data.data?.game_id) {
        // 跟踪成功获取随机游戏
        trackEvent('Navigation', 'RandomGame', 'Success', data.data.game_id);
        // 直接跳转到游戏页面
        router.push(`/game/${data.data.game_id}`);
      }
    } catch (error) {
      console.error('Failed to get random game:', error);
      // 跟踪错误
      trackEvent('Navigation', 'RandomGame', 'Error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleRandomGame}
      disabled={isLoading}
      className={`
        fixed bottom-8 left-1/2 -translate-x-1/2 z-50
        bg-[#00a6ff] hover:bg-[#0095ff] text-white font-bold
        py-3 px-6 rounded-full shadow-lg
        flex items-center gap-2
        transition-all hover:scale-105 active:scale-95
        disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100
      `}
    >
      <Dice5 className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
      <span>{isLoading ? 'Finding...' : 'Random Game'}</span>
    </button>
  );
}
