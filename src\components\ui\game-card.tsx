'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { shouldSkipImageOptimization, getSafeImageUrl } from '@/lib/image-utils';
import { trackEvent } from '@/lib/utils';

interface GameCardProps {
  imageUrl: string;
  title: string;
  href: string;
  isHot?: boolean;
  isNew?: boolean;
  isBest?: boolean;
  isEditorPick?: boolean;
  isLarge?: boolean;
}

export function GameCard({
  imageUrl,
  title,
  href,
  isHot = false,
  isNew = false,
  isBest = false,
  isEditorPick = false,
  isLarge = false
}: GameCardProps) {
  // 处理图片URL，检查是否需要跳过优化
  const safeImageUrl = getSafeImageUrl(imageUrl);
  const skipOptimization = shouldSkipImageOptimization(imageUrl);

  // 处理游戏卡片点击事件
  const handleGameClick = () => {
    trackEvent('Game', 'CardClick', title);
  };

  return (
    <div className="relative group">
      {isHot && (
        <div className={`absolute top-[8px] sm:top-[10px] -right-[10px] sm:-right-[14px] z-20 ${isLarge ? 'sm:top-[14px] sm:-right-[18px]' : ''}`}>
          <div className={`w-8 h-8 sm:w-12 sm:h-12 ${isLarge ? 'sm:w-16 sm:h-16' : ''}`}>
            <Image
              src="/images/hot.png"
              alt="Hot"
              width={48}
              height={48}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      )}

      {isNew && (
        <div className={`absolute top-[8px] sm:top-[10px] -right-[8px] sm:-right-[10px] z-20 ${isLarge ? 'sm:top-[14px] sm:-right-[14px]' : ''}`}>
          <div className={`w-8 h-8 sm:w-12 sm:h-12 ${isLarge ? 'sm:w-16 sm:h-16' : ''}`}>
            <Image
              src="/images/new.png"
              alt="New"
              width={48}
              height={48}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      )}

      {isBest && (
        <div className={`absolute top-[40px] sm:top-[50px] -right-[8px] sm:-right-[12px] z-20 ${isLarge ? 'sm:top-[70px] sm:-right-[16px]' : ''}`}>
          <div className={`w-8 h-8 sm:w-12 sm:h-12 ${isLarge ? 'sm:w-16 sm:h-16' : ''}`}>
            <Image
              src="/images/hot.png"
              alt="Best"
              width={48}
              height={48}
              className="w-full h-full object-contain"
            />
          </div>
        </div>
      )}

      <Link href={href} onClick={handleGameClick} className="block overflow-hidden rounded-xl group shadow-[0_6px_10px_rgba(0,0,0,0.2)] hover:shadow-[0_8px_15px_rgba(0,0,0,0.25)] transition-all duration-300">
        <div className="w-full aspect-square relative">
          <Image
            src={safeImageUrl}
            alt={title}
            fill
            unoptimized={skipOptimization}
            sizes={isLarge ? "(max-width: 768px) 33vw, 40vw" : "(max-width: 768px) 33vw, (max-width: 1200px) 25vw, 20vw"}
            className="object-cover rounded-xl transition-transform duration-300 group-hover:scale-110"
          />
        </div>
      </Link>
    </div>
  );
}
