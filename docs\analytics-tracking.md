# 百度统计精确跟踪配置

本项目已经配置了精确的百度统计跟踪，包括页面访问和用户行为事件跟踪。

## 已实现的跟踪功能

### 1. 自动页面访问跟踪
- **RouteTracker组件**: 自动监听Next.js路由变化，精确跟踪每次页面切换
- **位置**: `src/components/analytics/route-tracker.tsx`
- **功能**: 每次路由变化时自动调用 `_hmt.push(['_trackPageview', url])`

### 2. 用户行为事件跟踪

#### 游戏相关事件
- **游戏播放**: 用户点击"Play Game"按钮
  - 事件类别: `Game`
  - 事件动作: `Play`
  - 事件标签: 游戏标题
  - 事件值: 游戏ID

- **游戏卡片点击**: 用户点击游戏卡片
  - 事件类别: `Game`
  - 事件动作: `CardClick`
  - 事件标签: 游戏标题

#### 搜索事件
- **搜索提交**: 用户提交搜索表单或点击搜索图标
  - 事件类别: `Search`
  - 事件动作: `Submit` 或 `IconClick`
  - 事件标签: 搜索关键词

#### 导航事件
- **随机游戏**: 用户点击随机游戏按钮
  - 事件类别: `Navigation`
  - 事件动作: `RandomGame`
  - 事件标签: `ButtonClick`, `Success`, 或 `Error`

## 工具函数

### trackPageView(url?)
手动触发页面访问统计
```typescript
import { trackPageView } from '@/lib/utils';
trackPageView('/custom-page'); // 可选参数，默认使用当前页面URL
```

### trackEvent(category, action, label?, value?)
跟踪自定义事件
```typescript
import { trackEvent } from '@/lib/utils';
trackEvent('Game', 'Play', 'Minecraft', 123);
```

## 使用Analytics Context

对于复杂的用户行为跟踪，可以使用AnalyticsProvider提供的方法：

```typescript
import { useAnalytics } from '@/components/analytics/analytics-wrapper';

function MyComponent() {
  const { trackGameFavorite, trackGameLike, trackCategoryView } = useAnalytics();
  
  const handleFavorite = () => {
    trackGameFavorite('Game Title', 123, 'add');
  };
}
```

## 调试

在开发环境中，所有跟踪事件都会在控制台输出日志，格式如下：
- `百度统计 - 页面访问: /game/minecraft`
- `百度统计 - 事件跟踪: { category: 'Game', action: 'Play', label: 'Minecraft', value: 123 }`

## 注意事项

1. 所有跟踪函数都会检查 `window._hmt` 是否存在，确保在服务端渲染时不会出错
2. 路由跟踪有100ms的延迟，确保页面完全加载后再发送统计
3. 事件跟踪会自动过滤掉空值参数
4. 在生产环境中，确保百度统计脚本正确加载

## 百度统计后台查看

登录百度统计后台可以查看：
- **流量分析 > 页面分析**: 查看页面访问数据
- **访客分析 > 事件分析**: 查看自定义事件数据
- **实时访客**: 查看实时访问情况
