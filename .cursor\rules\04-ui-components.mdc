---
description:
globs:
alwaysApply: false
---
# UI组件系统

本项目使用Tailwind CSS构建UI，UI组件主要位于[`src/components/ui`](mdc:src/components/ui)目录中。

## 页面布局组件

- [`src/components/ui/page-layout.tsx`](mdc:src/components/ui/page-layout.tsx): 页面布局组件
- [`src/components/ui/page-header.tsx`](mdc:src/components/ui/page-header.tsx): 页面头部组件
- [`src/components/ui/page-content.tsx`](mdc:src/components/ui/page-content.tsx): 页面内容组件
- [`src/components/ui/page-description.tsx`](mdc:src/components/ui/page-description.tsx): 页面描述组件

## 导航组件

- [`src/components/ui/header.tsx`](mdc:src/components/ui/header.tsx): 网站头部导航组件
- [`src/components/ui/footer.tsx`](mdc:src/components/ui/footer.tsx): 网站底部组件
- [`src/components/ui/sidebar.tsx`](mdc:src/components/ui/sidebar.tsx): 侧边栏组件
- [`src/components/ui/back-button.tsx`](mdc:src/components/ui/back-button.tsx): 返回按钮组件

## 游戏相关组件

- [`src/components/ui/game-card.tsx`](mdc:src/components/ui/game-card.tsx): 游戏卡片组件
- [`src/components/ui/game-detail.tsx`](mdc:src/components/ui/game-detail.tsx): 游戏详情组件
- [`src/components/ui/game-iframe.tsx`](mdc:src/components/ui/game-iframe.tsx): 游戏iframe组件
- [`src/components/ui/game-section.tsx`](mdc:src/components/ui/game-section.tsx): 游戏板块组件
- [`src/components/ui/play-game-button.tsx`](mdc:src/components/ui/play-game-button.tsx): 游戏播放按钮
- [`src/components/ui/random-game-button.tsx`](mdc:src/components/ui/random-game-button.tsx): 随机游戏按钮

## 分页和搜索组件

- [`src/components/ui/pagination.tsx`](mdc:src/components/ui/pagination.tsx): 通用分页组件
- [`src/components/ui/category-pagination.tsx`](mdc:src/components/ui/category-pagination.tsx): 分类页分页组件
- [`src/components/ui/search-results.tsx`](mdc:src/components/ui/search-results.tsx): 搜索结果组件

## 开发注意事项

1. 创建新UI组件时应遵循现有组件的命名和结构风格
2. UI组件应使用Tailwind CSS进行样式管理
3. 确保UI组件适配移动端和桌面端
4. 避免修改现有UI组件的功能和样式，除非有明确的需求
