'use client';

import React, { ReactNode } from 'react';
import Link from 'next/link';
import { BackButton } from '@/components/ui/back-button';

interface PageLayoutProps {
  children: ReactNode;
}

export function PageLayout({ children }: PageLayoutProps) {
  return (
    <main className="min-h-screen bg-primary">
      {/* 使用BackButton组件 - 不再指定href参数，让组件自己决定返回位置 */}
      <BackButton />

      <div className="pt-20 sm:pt-24 pb-10 px-4 sm:px-6 max-w-7xl mx-auto">
        {children}
      </div>

      {/* Footer navigation */}
      <footer className="text-center py-4 space-y-2 pb-20">
        <div className="flex items-center justify-center gap-4 text-primary">
          <Link href="/about" className="hover:underline">About</Link>
          <Link href="/contact" className="hover:underline">Contact Us</Link>
          <Link href="/privacy" className="hover:underline">Privacy Statement</Link>
          <Link href="/terms" className="hover:underline">Terms of Use</Link>
          {/* <Link href="/blogs" className="hover:underline">Blogs</Link> */}
        </div>
        <div className="flex items-center justify-center gap-1 text-primary">
          <span className="font-bold">TAPIXY</span>
          <span>© TAPIXY {new Date().getFullYear()} www.tapixy.com</span>
        </div>
      </footer>
    </main>
  );
} 