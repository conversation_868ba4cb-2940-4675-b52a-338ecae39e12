'use client';

import React, { useState, useEffect } from 'react';
import { Maximize, RefreshCw } from 'lucide-react';
import { Game } from '@/types/game';

interface GameIframeInlineProps {
  game: Game;
}

export function GameIframeInline({ game }: GameIframeInlineProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [iframeKey, setIframeKey] = useState(Date.now());

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  const toggleFullscreen = async () => {
    try {
      const container = document.getElementById('inline-game-container');
      if (!container) return;

      if (!document.fullscreenElement) {
        await container.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (err) {
      console.error("Failed to toggle fullscreen:", err);
    }
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setIframeKey(Date.now()); // 重新加载iframe
  };

  return (
    <div 
      id="inline-game-container"
      className="relative w-full bg-gray-900 rounded-xl overflow-hidden"
      style={{ height: '600px' }} // 默认高度，可根据需要调整
    >
      {/* 控制按钮 */}
      <div className="absolute top-2 right-2 z-10 flex gap-2">
        <button
          onClick={handleRefresh}
          className="p-2 rounded-full bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm transition-colors"
          aria-label="Refresh"
        >
          <RefreshCw size={18} />
        </button>
        <button
          onClick={toggleFullscreen}
          className="p-2 rounded-full bg-black/50 hover:bg-black/70 text-white backdrop-blur-sm transition-colors"
          aria-label="Fullscreen"
        >
          <Maximize size={18} />
        </button>
      </div>
      
      {/* 游戏加载指示器 */}
      {isLoading && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-gray-900 text-white z-5">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-blue-500"></div>
          <p className="mt-4">Loading game...</p>
        </div>
      )}

      {/* 游戏iframe */}
      <iframe
        key={iframeKey}
        src={game.url}
        title={game.title}
        className="w-full h-full border-0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        onLoad={handleIframeLoad}
      />
    </div>
  );
} 