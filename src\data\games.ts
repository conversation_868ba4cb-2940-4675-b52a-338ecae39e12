import { Game } from "@/types/game";

export const gamesData: Game[] = [
  {
    id: "car-racing",
    title: "Car Racing",
    image: "https://ext.same-assets.com/2873387825/1569078536.jpeg",
    isHot: true,
    category: "racing",
    url: "https://example.com/games/car-racing"
  },
  {
    id: "fridge-cleanup",
    title: "Fridge Cleanup",
    image: "https://ext.same-assets.com/2873387825/1062497655.jpeg",
    category: "puzzle",
    url: "https://example.com/games/fridge-cleanup"
  },
  {
    id: "block-puzzle",
    title: "Block Puzzle",
    image: "https://ext.same-assets.com/2873387825/535094163.jpeg",
    isHot: true,
    category: "puzzle",
    url: "https://example.com/games/block-puzzle"
  },
  {
    id: "sword-judgement",
    title: "Sword Judgement",
    image: "https://ext.same-assets.com/2873387825/3987655339.jpeg",
    isNew: true,
    category: "action",
    url: "https://example.com/games/sword-judgement"
  },
  {
    id: "bird-sort",
    title: "Bird Sort Puzzle",
    image: "https://ext.same-assets.com/2873387825/4182098242.jpeg",
    category: "puzzle",
    url: "https://example.com/games/bird-sort"
  },
  {
    id: "frog-jump",
    title: "Frog Jump",
    image: "https://ext.same-assets.com/2873387825/1445822146.jpeg",
    isHot: true,
    category: "arcade"
  },
  {
    id: "candy-blocks",
    title: "Candy Blocks",
    image: "https://ext.same-assets.com/2873387825/2127849706.jpeg",
    category: "puzzle"
  },
  {
    id: "bubble-time",
    title: "Bubble Time",
    image: "https://ext.same-assets.com/2873387825/2057926645.jpeg",
    isNew: true,
    category: "arcade"
  },
  {
    id: "piano-tiles",
    title: "Piano Tiles",
    image: "https://ext.same-assets.com/2873387825/2958239190.jpeg",
    isHot: true,
    category: "music"
  },
  {
    id: "subway-surfers",
    title: "Subway Surfers",
    image: "https://ext.same-assets.com/2873387825/1569078536.jpeg",
    isHot: true,
    category: "arcade"
  },
  {
    id: "temple-run",
    title: "Temple Run 2",
    image: "https://ext.same-assets.com/2873387825/2958239190.jpeg",
    isHot: true,
    category: "arcade"
  },
  {
    id: "fruit-ninja",
    title: "Fruit Ninja",
    image: "https://ext.same-assets.com/2873387825/1062497655.jpeg",
    isHot: true,
    category: "arcade"
  },
  {
    id: "angry-birds",
    title: "Angry Birds",
    image: "https://ext.same-assets.com/2873387825/4182098242.jpeg",
    isHot: true,
    category: "puzzle"
  },
  {
    id: "candy-crush",
    title: "Candy Crush",
    image: "https://ext.same-assets.com/2873387825/2127849706.jpeg",
    isHot: true,
    category: "puzzle"
  },
  {
    id: "among-us",
    title: "Among Us",
    image: "https://ext.same-assets.com/2873387825/3987655339.jpeg",
    isNew: true,
    category: "social"
  },
  {
    id: "fall-guys",
    title: "Fall Guys",
    image: "https://ext.same-assets.com/2873387825/1445822146.jpeg",
    isNew: true,
    category: "action"
  },
  {
    id: "rocket-league",
    title: "Rocket League",
    image: "https://ext.same-assets.com/2873387825/1569078536.jpeg",
    isNew: true,
    category: "sports"
  },
  {
    id: "fortnite",
    title: "Fortnite",
    image: "https://ext.same-assets.com/2873387825/2958239190.jpeg",
    isNew: true,
    category: "action"
  },
  {
    id: "minecraft",
    title: "Minecraft",
    image: "https://ext.same-assets.com/2873387825/535094163.jpeg",
    isNew: true,
    category: "adventure"
  },
  {
    id: "pubg-mobile",
    title: "PUBG Mobile",
    image: "https://ext.same-assets.com/2873387825/3987655339.jpeg",
    isFree: true,
    category: "action"
  },
  {
    id: "call-of-duty",
    title: "Call of Duty",
    image: "https://ext.same-assets.com/2873387825/1569078536.jpeg",
    isFree: true,
    category: "action"
  },
  {
    id: "genshin-impact",
    title: "Genshin Impact",
    image: "https://ext.same-assets.com/2873387825/4182098242.jpeg",
    isFree: true,
    category: "rpg"
  },
  {
    id: "apex-legends",
    title: "Apex Legends",
    image: "https://ext.same-assets.com/2873387825/2958239190.jpeg",
    isFree: true,
    category: "action"
  },
  {
    id: "valorant",
    title: "Valorant",
    image: "https://ext.same-assets.com/2873387825/1445822146.jpeg",
    isFree: true,
    category: "shooter"
  },
  {
    id: "league-of-legends",
    title: "League of Legends",
    image: "https://ext.same-assets.com/2873387825/535094163.jpeg",
    isFree: true,
    category: "moba"
  },
  {
    id: "minecraft-pocket",
    title: "Minecraft Pocket",
    image: "https://ext.same-assets.com/2873387825/2127849706.jpeg",
    isRanked: true,
    rank: 1,
    category: "adventure"
  },
  {
    id: "roblox",
    title: "Roblox",
    image: "https://ext.same-assets.com/2873387825/2057926645.jpeg",
    isRanked: true,
    rank: 2,
    category: "adventure"
  },
  {
    id: "clash-of-clans",
    title: "Clash of Clans",
    image: "https://ext.same-assets.com/2873387825/1062497655.jpeg",
    isRanked: true,
    rank: 3,
    category: "strategy"
  },
  {
    id: "pokemon-go",
    title: "Pokemon GO",
    image: "https://ext.same-assets.com/2873387825/1569078536.jpeg",
    isRanked: true,
    rank: 4,
    category: "adventure"
  },
  {
    id: "candy-crush-saga",
    title: "Candy Crush Saga",
    image: "https://ext.same-assets.com/2873387825/4182098242.jpeg",
    isRanked: true,
    rank: 5,
    category: "puzzle"
  },
  {
    id: "pubg-battle",
    title: "PUBG Battlegrounds",
    image: "https://ext.same-assets.com/2873387825/3987655339.jpeg",
    isRanked: true,
    rank: 6,
    category: "action"
  },
  {
    id: "fifa-mobile",
    title: "FIFA Mobile",
    image: "https://ext.same-assets.com/2873387825/2958239190.jpeg",
    isRanked: true,
    rank: 7,
    category: "sports"
  },
  {
    id: "gardenscapes",
    title: "Gardenscapes",
    image: "https://ext.same-assets.com/2873387825/1445822146.jpeg",
    isRanked: true,
    rank: 8,
    category: "puzzle"
  }
];

export function getTrendingGames(): Game[] {
  return gamesData.slice(0, 12);
}

export function getHotGames(): Game[] {
  return gamesData.filter(game => game.isHot).slice(0, 12);
}

export function getNewGames(): Game[] {
  return gamesData.filter(game => game.isNew).slice(0, 12);
}

export function getArcadeGames(): Game[] {
  return gamesData.filter(game => game.category === "arcade").slice(0, 12);
}

export function getRacingGames(): Game[] {
  return gamesData.filter(game => game.category === "racing").slice(0, 12);
}

export function getPuzzleGames(): Game[] {
  return gamesData.filter(game => game.category === "puzzle").slice(0, 12);
}

export function getAdventureGames(): Game[] {
  return gamesData.filter(game => game.category === "adventure").slice(0, 12);
}

export function getAllGames(): Game[] {
  return gamesData.slice(0, 24);
}

export function getRankedGames(): Game[] {
  return gamesData.filter(game => game.isRanked).sort((a, b) => (a.rank || 0) - (b.rank || 0)).slice(0, 12);
}

export function getFreeGames(): Game[] {
  return gamesData.filter(game => game.isFree).slice(0, 12);
}

export function getActionGames(): Game[] {
  return gamesData.filter(game => game.category === "action").slice(0, 12);
}

export function getSportsGames(): Game[] {
  return gamesData.filter(game => game.category === "sports").slice(0, 12);
}
