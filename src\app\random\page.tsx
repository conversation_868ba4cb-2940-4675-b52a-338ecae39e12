import Link from "next/link";
// import { Header } from "@/components/ui/header";
import { Sidebar } from "@/components/ui/sidebar";
import { fetchLatestGames } from "@/lib/api";
import { Game } from "@/types/game";

// Revalidate the page every 1 hour (3600 seconds)
export const revalidate = 3600;

export default async function RandomPage() {
  // Get some games to display
  const gamesData = await fetchLatestGames(1, 20);
  const games: Game[] = gamesData.games || [];
  
  return (
    <main className="min-h-screen relative">
      <Sidebar />

      <div className="ml-16">
        {/* <Header /> */}

        <div className="flex flex-col items-center justify-center min-h-[70vh] px-8 text-center">
          <h1 className="text-3xl font-bold mb-6">Random Game Finder</h1>
          <p className="text-xl mb-8 max-w-2xl">
            Click on any game below to start your gaming adventure!
          </p>

          <div className="flex gap-4 flex-wrap justify-center">
            {games.map((game: Game) => (
              <Link
                key={game.id}
                href={`/game/${game.id}`}
                className="bg-white px-4 py-2 rounded-lg hover:bg-gray-100 transition"
              >
                {game.title}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </main>
  );
}
