'use client';

import { createContext, useContext, ReactNode } from 'react';
import { trackEvent } from '@/lib/utils';

interface AnalyticsContextType {
  trackGameFavorite: (gameTitle: string, gameId: number, action: 'add' | 'remove') => void;
  trackGameLike: (gameTitle: string, gameId: number, action: 'add' | 'remove') => void;
  trackCategoryView: (categoryName: string) => void;
  trackSearchResult: (keyword: string, resultCount: number) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

export function AnalyticsProvider({ children }: { children: ReactNode }) {
  const trackGameFavorite = (gameTitle: string, gameId: number, action: 'add' | 'remove') => {
    trackEvent('Game', `Favorite_${action}`, gameTitle, gameId);
  };

  const trackGameLike = (gameTitle: string, gameId: number, action: 'add' | 'remove') => {
    trackEvent('Game', `Like_${action}`, gameTitle, gameId);
  };

  const trackCategoryView = (categoryName: string) => {
    trackEvent('Navigation', 'CategoryView', categoryName);
  };

  const trackSearchResult = (keyword: string, resultCount: number) => {
    trackEvent('Search', 'Result', keyword, resultCount);
  };

  return (
    <AnalyticsContext.Provider value={{
      trackGameFavorite,
      trackGameLike,
      trackCategoryView,
      trackSearchResult
    }}>
      {children}
    </AnalyticsContext.Provider>
  );
}

export function useAnalytics() {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalytics must be used within an AnalyticsProvider');
  }
  return context;
}
