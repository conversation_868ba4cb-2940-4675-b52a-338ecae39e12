---
description:
globs:
alwaysApply: false
---
# 开发工作流程

本项目使用Next.js和Tailwind CSS进行开发，以下是开发工作流程和注意事项。

## 开发环境设置

项目使用以下命令进行开发：

- `npm run dev`: 启动开发服务器（0.0.0.0地址，支持局域网访问）
- `npm run build`: 构建生产版本
- `npm run start`: 启动生产服务器
- `npm run lint`: 运行代码检查
- `npm run format`: 格式化代码

## 新功能开发流程

1. **分析需求**：明确需求并确定是否需要创建新组件或修改现有组件
2. **设计组件**：在创建新组件前，先查看现有组件的命名和结构风格
3. **实现功能**：按照项目规范实现功能
4. **样式开发**：使用Tailwind CSS实现样式，确保移动端适配
5. **测试功能**：确保功能在不同设备上正常工作
6. **优化SEO**：确保页面符合SEO标准

## 开发规范

1. **不修改原有功能**：如果需要添加新功能，创建新组件和页面，不修改现有组件
2. **API使用**：不随意修改现有API接口，按照明确需求精准修改
3. **数据结构**：不随意修改游戏数据结构，保持一致性
4. **移动端适配**：确保所有页面和组件在移动设备上正常显示
5. **SEO优化**：使用适当的标题、描述和结构化数据

## 项目部署

项目支持以下部署选项：

- Next.js默认部署
- 静态导出（`next build` 和 `next export`）
- Netlify部署（使用`netlify.toml`配置）
