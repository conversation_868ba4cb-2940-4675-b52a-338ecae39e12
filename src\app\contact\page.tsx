import { Metadata } from 'next';
import { PageLayout } from '@/components/ui/page-layout';
import { PageContent } from '@/components/ui/page-content';
import { fetchPageContent } from '@/lib/api';

export const metadata: Metadata = {
  title: 'Contact Us - TapixyGames',
  description: 'Get in touch with the TapixyGames team. We\'re here to help with any questions or feedback you may have.',
  openGraph: {
    title: 'Contact Us - TapixyGames',
    description: 'Get in touch with the TapixyGames team. We\'re here to help with any questions or feedback you may have.',
  },
};

export default async function ContactPage() {
  // 尝试从API获取页面内容
  const pageData = await fetchPageContent('/contact');
  
  // 定义默认内容，当API请求失败时使用
  const defaultDescription = "Have a question or feedback? We'd love to hear from you! Get in touch with the TapixyGames team and we'll get back to you as soon as possible.";
  const defaultContent = `
    <div class="max-w-2xl mx-auto">
      <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Get In Touch</h2>
      <p class="mb-6">We value your feedback and are here to assist with any questions or concerns you may have. Please use the contact information below to reach out to us.</p>
      
      <div class="grid md:grid-cols-2 gap-8 mb-8">
        <div>
          <h3 class="text-lg font-semibold text-[#002b50] mb-2">Contact Information</h3>
          <ul class="space-y-3">
            <li class="flex items-start">
              <span class="font-medium mr-2">Email:</span>
              <span><EMAIL></span>
            </li>
            <li class="flex items-start">
              <span class="font-medium mr-2">Hours:</span>
              <span>Monday to Friday, 9am - 5pm GMT</span>
            </li>
            <li class="flex items-start">
              <span class="font-medium mr-2">Response time:</span>
              <span>We aim to respond to all inquiries within 24-48 hours</span>
            </li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold text-[#002b50] mb-2">Submit Feedback</h3>
          <p>We're always looking to improve our platform. If you have suggestions or encountered any issues, please let us know.</p>
          <p class="mt-3">You can submit feedback by emailing us at <a href="mailto:<EMAIL>" class="text-blue-500 hover:underline"><EMAIL></a></p>
        </div>
      </div>
      
      <div class="bg-blue-50 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-[#002b50] mb-2">Developer Inquiries</h3>
        <p>Are you a game developer interested in featuring your games on TapixyGames? We'd love to hear from you!</p>
        <p class="mt-3">Please contact our developer relations team at <a href="mailto:<EMAIL>" class="text-blue-500 hover:underline"><EMAIL></a></p>
      </div>
    </div>
  `;

  return (
    <PageLayout>
      <PageContent
        title="Contact Us"
        description={pageData?.description || defaultDescription}
        content={pageData?.hasContent ? pageData.content : defaultContent}
      />
    </PageLayout>
  );
} 