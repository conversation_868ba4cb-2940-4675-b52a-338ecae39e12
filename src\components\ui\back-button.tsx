'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ChevronLeft } from 'lucide-react';
import { useBackButton } from '@/contexts/BackButtonContext';
import { handleSmartBack } from '@/lib/navigation-utils';

interface BackButtonProps {
  href?: string; // 保留href参数以向后兼容
}

export function BackButton({ href = '/' }: BackButtonProps) {
  const router = useRouter();
  const { setHasBackButton } = useBackButton();

  // 当组件挂载时，通知上下文存在返回按钮
  useEffect(() => {
    setHasBackButton(true);
    
    // 当组件卸载时，重置状态
    return () => {
      setHasBackButton(false);
    };
  }, [setHasBackButton]);

  // 智能返回处理函数
  const handleBackClick = (e: React.MouseEvent) => {
    e.preventDefault(); // 阻止默认链接行为
    handleSmartBack(router); // 使用智能返回函数
  };

  return (
    <button
      onClick={handleBackClick}
      className="fixed top-2 sm:top-2 left-3 z-50 w-10 h-10 bg-white/80 backdrop-blur rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors"
      aria-label="返回"
    >
      <ChevronLeft className="w-6 h-6 text-[#002b50]" />
    </button>
  );
} 