'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Game } from '@/types/game';

interface ToggleFavoriteResponse {
  favoriteCount: number;
  isFavorite: boolean;
}

interface FavoriteGamesContextType {
  favoriteGames: Game[];
  addFavorite: (game: Game) => Promise<ToggleFavoriteResponse>;
  removeFavorite: (gameId: string) => Promise<ToggleFavoriteResponse>;
  clearFavorites: () => void;
  isFavorite: (gameId: string) => boolean;
}

const STORAGE_KEY = 'favorite-games';
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://tapixyapi.qilaigames.com';

const FavoriteGamesContext = createContext<FavoriteGamesContextType>({
  favoriteGames: [],
  addFavorite: async () => ({ favoriteCount: 0, isFavorite: false }),
  removeFavorite: async () => ({ favoriteCount: 0, isFavorite: false }),
  clearFavorites: () => {},
  isFavorite: () => false,
});

export function FavoriteGamesProvider({ children }: { children: React.ReactNode }) {
  const [favoriteGames, setFavoriteGames] = useState<Game[]>([]);

  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        setFavoriteGames(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading favorite games:', error);
    }
  }, []);

  useEffect(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(favoriteGames));
    } catch (error) {
      console.error('Error saving favorite games:', error);
    }
  }, [favoriteGames]);

  const toggleFavorite = async (gameId: string, action: 'add' | 'remove'): Promise<ToggleFavoriteResponse> => {
    try {
      // 构建API URL和参数
      const url = new URL(`${API_BASE_URL}/games/toggle-favorite/${gameId}`);
      url.searchParams.append('action', action);

      // 调用后端 API
      const response = await fetch(url.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: action
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to toggle favorite');
      }

      return {
        favoriteCount: data.favorite_count || 0,
        isFavorite: data.is_favorite || action === 'add'
      };
    } catch (error) {
      console.error('Error toggling favorite:', error);
      // 返回一个默认值，保持UI一致性
      return {
        favoriteCount: 0,
        isFavorite: action === 'add'
      };
    }
  };

  const addFavorite = async (game: Game): Promise<ToggleFavoriteResponse> => {
    // 先更新本地状态，让UI立即反应
    setFavoriteGames(prev => {
      if (!prev.some(g => g.id === game.id)) {
        return [...prev, game];
      }
      return prev;
    });
    
    // 与后端交互，获取更新后的收藏数
    return await toggleFavorite(game.id, 'add');
  };

  const removeFavorite = async (gameId: string): Promise<ToggleFavoriteResponse> => {
    // 先更新本地状态，让UI立即反应
    setFavoriteGames(prev => prev.filter(game => game.id !== gameId));
    
    // 与后端交互，获取更新后的收藏数
    return await toggleFavorite(gameId, 'remove');
  };

  const clearFavorites = () => {
    setFavoriteGames([]);
  };

  const isFavorite = (gameId: string) => {
    return favoriteGames.some(game => game.id === gameId);
  };

  return (
    <FavoriteGamesContext.Provider
      value={{
        favoriteGames,
        addFavorite,
        removeFavorite,
        clearFavorites,
        isFavorite,
      }}
    >
      {children}
    </FavoriteGamesContext.Provider>
  );
}

export function useFavoriteGames() {
  const context = useContext(FavoriteGamesContext);
  if (!context) {
    throw new Error('useFavoriteGames must be used within a FavoriteGamesProvider');
  }
  return context;
} 