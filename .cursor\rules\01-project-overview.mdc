---
description:
globs:
alwaysApply: false
---
# 项目概述

这是一个使用Next.js和Tailwind CSS开发的小游戏网站 (www.tapixy.com)。网站主要提供各种在线小游戏，面向英语用户群体，设计风格符合欧美用户审美。

## 主要技术栈
- Next.js 15.x (React 框架)
- Tailwind CSS (样式)
- TypeScript
- 自定义API集成

## 核心项目目标
1. 提供简单易用的游戏发现和体验平台
2. 优化移动端和桌面端体验
3. 符合SEO标准，提升搜索引擎可见性

## 开发规范
1. 不修改现有功能，新功能应创建新组件和页面
2. 不随意修改已有API接口，按照明确要求进行精准修改
3. 保持游戏数据结构的一致性
4. 测试环境API接口来源: https://tapixyapi.qilaigames.com
