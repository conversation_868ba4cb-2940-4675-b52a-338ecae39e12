import { Metadata } from 'next';
import { PageLayout } from '@/components/ui/page-layout';
import { PageContent } from '@/components/ui/page-content';
import { fetchPageContent } from '@/lib/api';

export const metadata: Metadata = {
  title: 'Privacy Statement - TapixyGames',
  description: 'Our privacy policy explains how we collect, use, and protect your information when you visit our website.',
  openGraph: {
    title: 'Privacy Statement - TapixyGames',
    description: 'Our privacy policy explains how we collect, use, and protect your information when you visit our website.',
  },
};

export default async function PrivacyPage() {
  // 尝试从API获取页面内容
  const pageData = await fetchPageContent('/privacy');
  
  // 定义默认内容，当API请求失败时使用
  const defaultDescription = "Our privacy policy explains how we collect, use, and protect your information when you visit TapixyGames. We are committed to safeguarding your privacy and ensuring a secure gaming experience.";
  const defaultContent = `
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Information We Collect</h2>
    <p>We collect information that helps us provide and improve our services. This may include:</p>
    <ul class="list-disc pl-5 my-3 space-y-1">
      <li>Basic account information (when you create an account)</li>
      <li>Usage data and preferences</li>
      <li>Device information and IP address</li>
      <li>Cookies and similar technologies</li>
    </ul>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">How We Use Your Information</h2>
    <p>We use the information we collect to:</p>
    <ul class="list-disc pl-5 my-3 space-y-1">
      <li>Provide and maintain our services</li>
      <li>Improve and personalize your experience</li>
      <li>Communicate with you about updates and new features</li>
      <li>Ensure the security of our platform</li>
    </ul>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Data Security</h2>
    <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Your Choices</h2>
    <p>You have the right to access, correct, or delete your personal information. You can also choose to disable cookies through your browser settings.</p>
    
    <h2 class="text-xl font-bold text-[#002b50] mt-6 mb-3">Contact Us</h2>
    <p>If you have questions about our Privacy Policy, please contact us through our <a href="/contact" class="text-blue-500 hover:underline">Contact page</a>.</p>
  `;

  return (
    <PageLayout>
      <PageContent
        title="Privacy Statement"
        description={pageData?.description || defaultDescription}
        content={pageData?.hasContent ? pageData.content : defaultContent}
      />
    </PageLayout>
  );
} 