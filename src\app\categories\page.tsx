import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { ClientWrapper } from "@/components/client-wrapper";
import { getCategories } from "@/lib/api";

export const metadata: Metadata = {
  title: 'Game Categories | TapixyGames',
  description: 'Browse all game categories on TapixyGames. Find action games, racing games, puzzle games, and more!',
};

export default async function CategoriesPage() {
  const categories = await getCategories();

  return (
    <main className="min-h-screen bg-primary">
      <ClientWrapper>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-8">Game Categories</h1>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 sm:gap-6">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.name.toLowerCase().replace(/\s+/g, '-')}`}
                className="group bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-200"
              >
                <div className="aspect-square relative mb-3 rounded-lg overflow-hidden">
                  <Image
                    src={category.image.startsWith('http') ? category.image : `/images${category.image}`}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-200"
                  />
                </div>
                <h2 className="text-center text-gray-900 font-medium group-hover:text-blue-600 transition-colors">
                  {category.name}
                </h2>
              </Link>
            ))}
          </div>
        </div>
      </ClientWrapper>
    </main>
  );
} 