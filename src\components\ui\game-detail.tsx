'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Game } from '@/types/game';
import { PlayGameButton } from './play-game-button';
import { useFavoriteGames } from '@/contexts/FavoriteGamesContext';
import { useLikedGames } from '@/contexts/LikedGamesContext';
import { ChevronLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { GameIframeInline } from './game-iframe-inline';
import { shouldSkipImageOptimization, getSafeImageUrl } from '@/lib/image-utils';
import { useBackButton } from '@/contexts/BackButtonContext';
import { handleSmartBack } from '@/lib/navigation-utils';
import { getGameDetailUrl } from '@/lib/url-utils';

interface GameDetailProps {
  game: Game;
  recommendedGames: Game[];
}

// 创建一个安全的HTML渲染组件
function SafeHTML({ content }: { content: string }) {
  return (
    <div 
      className="prose prose-lg max-w-none text-gray-700"
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}

// 广告组件占位符
function AdPlaceholder({ className }: { className?: string }) {
  return (
    <div className={`bg-gray-200 rounded-lg flex items-center justify-center p-4 ${className}`}>
      {/* 移除文字内容，保留空白占位符 */}
    </div>
  );
}

export function GameDetail({ game, recommendedGames }: GameDetailProps) {
  const router = useRouter();
  const { setHasBackButton } = useBackButton();
  const { addFavorite, removeFavorite, isFavorite } = useFavoriteGames();
  const { toggleLike, isLiked } = useLikedGames();
  const isGameFavorite = isFavorite(game.id);
  const isGameLiked = isLiked(game.id);
  const [showShareToast, setShowShareToast] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  
  // 添加本地状态来跟踪点赞和收藏数
  const [likeCount, setLikeCount] = useState<number>(game.like_count || 0);
  const [favoriteCount, setFavoriteCount] = useState<number>(game.favorite_count || 0);
  // 增加一个状态跟踪屏幕大小
  const [isMobile, setIsMobile] = useState(false);

  // 使用useEffect检测屏幕大小并设置返回按钮状态
  useEffect(() => {
    // 检测当前是否为移动设备（小屏幕）
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 640); // 640px是sm断点的值
      
      // 只在移动端设置hasBackButton为true
      if (window.innerWidth < 640) {
        setHasBackButton(true);
      } else {
        setHasBackButton(false);
      }
    };
    
    // 初始检查
    checkIfMobile();
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkIfMobile);
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', checkIfMobile);
      setHasBackButton(false);
    };
  }, [setHasBackButton]);

  const handleBack = () => {
    handleSmartBack(router);
  };

  const handleFavoriteClick = async () => {
    try {
      if (isGameFavorite) {
        const result = await removeFavorite(game.id);
        setFavoriteCount(result.favoriteCount);
      } else {
        const result = await addFavorite(game);
        setFavoriteCount(result.favoriteCount);
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  const handleLikeClick = async () => {
    try {
      const result = await toggleLike(game.id);
      setLikeCount(result.likeCount);
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: game.title,
          text: `Play ${game.title} on TapixyGames`,
          url: window.location.href
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        setShowShareToast(true);
        setTimeout(() => setShowShareToast(false), 2000);
      }
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handlePlayGame = () => {
    setIsPlaying(true);
    // 滚动到游戏区域
    setTimeout(() => {
      const gameElement = document.getElementById('game-section');
      if (gameElement) {
        gameElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  // 处理tags数据，确保它是一个数组
  const gameTags = React.useMemo(() => {
    if (!game.tags) return [] as string[];
    if (Array.isArray(game.tags)) return game.tags;
    if (typeof game.tags === 'string') {
      return (game.tags as string).split(',').map((tag: string) => tag.trim());
    }
    return [] as string[];
  }, [game.tags]);

  return (
    <div className="min-h-screen bg-primary relative pt-14">
      {/* 返回按钮 - 只在移动端显示 */}
      <button
        onClick={handleBack}
        className="sm:hidden fixed top-3 left-3 z-50 w-9 h-9 bg-white/80 backdrop-blur rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors"
        aria-label="返回"
      >
        <ChevronLeft className="w-5 h-5 text-[#002b50]" />
      </button>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-8">
          <div className="flex flex-col items-center text-center">
            {/* 游戏图标 */}
            <div className="relative w-32 h-32 sm:w-40 sm:h-40 mb-4">
              <Image
                src={getSafeImageUrl(game.image)}
                alt={game.title}
                fill
                unoptimized={shouldSkipImageOptimization(game.image)}
                className="object-cover rounded-2xl"
                sizes="(max-width: 640px) 128px, 160px"
                priority
              />

              {game.isHot && (
                <span className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                  Hot
                </span>
              )}

              {game.isNew && (
                <span className="absolute top-2 left-[60px] bg-green-500 text-white text-xs px-2 py-1 rounded">
                  New
                </span>
              )}
            </div>

            {/* 游戏标题 */}
            <h1 className="text-xl sm:text-3xl font-bold text-[#002b50] mb-4">{game.title}</h1>

            {/* 游戏分类和标签 */}
            <div className="flex flex-wrap gap-1.5 justify-center mb-6">
              {game.category && (
                <Link
                  href={`/category/${encodeURIComponent(game.category.toLowerCase())}`}
                  className="px-3 py-1 bg-[#f0f4f8] hover:bg-[#e1e7ef] rounded-full text-xs font-medium text-[#002b50] transition-colors"
                >
                  {game.category}
                </Link>
              )}
              {gameTags.slice(0, 3).map((tag: string) => (
                <Link
                  key={tag}
                  href={`/tag/name/${encodeURIComponent(tag.toLowerCase())}`}
                  className="px-3 py-1 bg-[#e8f0fe] hover:bg-[#d8e8fe] rounded-full text-xs font-medium text-[#1a73e8] transition-colors"
                >
                  {tag}
                </Link>
              ))}
            </div>

            {/* Play Now 按钮和功能按钮组 */}
            <div className="flex flex-col items-center gap-4 w-full max-w-md">
              <PlayGameButton game={game} onPlay={handlePlayGame} />
              
              {/* 功能按钮组 */}
              <div className="flex items-start justify-center gap-6 mt-2">
                {/* 收藏按钮和计数 */}
                <div className="flex flex-col items-center">
                  <button
                    onClick={handleFavoriteClick}
                    className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors mb-1 ${
                      isGameFavorite ? 'bg-yellow-100' : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <svg
                      viewBox="0 0 24 24"
                      width="20"
                      height="20"
                      className={`fill-current ${isGameFavorite ? 'text-yellow-500' : 'text-gray-500'}`}
                    >
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>
                  </button>
                  <span className="text-xs font-medium text-gray-600">
                    {favoriteCount.toLocaleString()}
                  </span>
                </div>

                {/* 点赞按钮和计数 */}
                <div className="flex flex-col items-center">
                  <button
                    onClick={handleLikeClick}
                    className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors mb-1 ${
                      isGameLiked ? 'bg-red-100' : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                  >
                    <svg
                      viewBox="0 0 24 24"
                      width="20"
                      height="20"
                      className={`fill-current ${isGameLiked ? 'text-red-500' : 'text-gray-500'}`}
                    >
                      <path d="M1 21h4V9H1v12zm22-11c0-1.1-.9-2-2-2h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 1 7.59 7.59C7.22 7.95 7 8.45 7 9v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-2z" />
                    </svg>
                  </button>
                  <span className="text-xs font-medium text-gray-600">
                    {likeCount.toLocaleString()}
                  </span>
                </div>

                {/* 分享按钮 */}
                <div className="flex flex-col items-center">
                  <button
                    onClick={handleShare}
                    className="w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors mb-1"
                  >
                    <svg
                      viewBox="0 0 24 24"
                      width="20"
                      height="20"
                      className="fill-current text-gray-500"
                    >
                      <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92c0-1.61-1.31-2.92-2.92-2.92z" />
                    </svg>
                  </button>
                  <span className="text-xs font-medium text-gray-600">Share</span>
                </div>
              </div>
            </div>

            {/* 分享成功提示 */}
            {showShareToast && (
              <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white py-2 px-4 rounded-lg text-sm">
                Link copied to clipboard
              </div>
            )}

            {/* 游戏区域 */}
            <div id="game-section" className="w-full mt-8">
              {/* 如果点击了Play Now按钮，显示游戏和广告位 */}
              {isPlaying && (
                <div className="flex flex-col lg:flex-row w-full gap-6">
                  {/* 左侧广告位 */}
                  <div className="hidden lg:block lg:w-[160px] h-600 shrink-0">
                    <AdPlaceholder className="h-[600px] w-full" />
                  </div>

                  {/* 游戏框架 */}
                  <div className="flex-grow">
                    <GameIframeInline game={game} />
                  </div>

                  {/* 右侧广告位 */}
                  <div className="hidden lg:block lg:w-[160px] h-600 shrink-0">
                    <AdPlaceholder className="h-[600px] w-full" />
                  </div>
                </div>
              )}

              {/* 提示信息 */}
              {!isPlaying && (
                <div className="bg-blue-50 text-blue-600 p-4 rounded-xl text-center">
                  Click the Play Now button to start playing!
                </div>
              )}

              {/* 移动端游戏下方广告位 */}
              {isPlaying && (
                <div className="mt-4 lg:hidden">
                  <AdPlaceholder className="h-[100px] w-full" />
                </div>
              )}
            </div>

            {/* 游戏说明 */}
            <div className="mt-10 text-left w-full">
              <h2 className="text-xl font-bold text-[#002b50] mb-4">About {game.title}</h2>
              
              {game.description ? (
                <SafeHTML content={game.description} />
              ) : (
                <p className="text-gray-600">
                  Enjoy playing {game.title} online! This game can be played directly in your browser, 
                  no downloads required.
                </p>
              )}
              
              {game.instructions && (
                <div className="mt-6">
                  <h3 className="text-lg font-bold text-[#002b50] mb-2">How to Play</h3>
                  <SafeHTML content={game.instructions} />
                </div>
              )}
            </div>

            {/* 游戏标签全显示 */}
            {gameTags.length > 0 && (
              <div className="mt-8 w-full">
                <h3 className="text-lg font-bold text-[#002b50] mb-3 text-left">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {gameTags.map((tag: string) => (
                    <Link
                      key={tag}
                      href={`/tag/name/${encodeURIComponent(tag.toLowerCase())}`}
                      className="px-3 py-1 bg-[#e8f0fe] hover:bg-[#d8e8fe] rounded-full text-xs font-medium text-[#1a73e8] transition-colors"
                    >
                      {tag}
                    </Link>
                  ))}
                </div>
              </div>
            )}

            {/* 推荐游戏 */}
            {recommendedGames.length > 0 && (
              <div className="mt-12 w-full">
                <h2 className="text-xl font-bold text-[#002b50] mb-6 text-left">You may also like</h2>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {recommendedGames.slice(0, 10).map((recGame) => (
                    <Link
                      key={recGame.id}
                      href={getGameDetailUrl(recGame)}
                      className="group"
                    >
                      <div className="aspect-square relative rounded-xl overflow-hidden mb-2">
                        <Image
                          src={getSafeImageUrl(recGame.image)}
                          alt={recGame.title}
                          fill
                          unoptimized={shouldSkipImageOptimization(recGame.image)}
                          className="object-cover transition-transform duration-300 group-hover:scale-110"
                          sizes="(max-width: 640px) 150px, 200px"
                        />
                      </div>
                      <h3 className="text-sm font-medium text-[#002b50] line-clamp-2">{recGame.title}</h3>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 