import { Metadata } from 'next';
import { PageLayout } from '@/components/ui/page-layout';
import { PageContent } from '@/components/ui/page-content';
import { fetchPageContent } from '@/lib/api';

export const metadata: Metadata = {
  title: 'Gaming Blogs - TapixyGames',
  description: 'Discover gaming tips, reviews, and news on the TapixyGames blog. Stay updated with the latest gaming trends and releases.',
  openGraph: {
    title: 'Gaming Blogs - TapixyGames',
    description: 'Discover gaming tips, reviews, and news on the TapixyGames blog. Stay updated with the latest gaming trends and releases.',
  },
};

export default async function BlogsPage() {
  // 尝试从API获取页面内容
  const pageData = await fetchPageContent('/blogs');
  
  // 定义默认内容，当API请求失败时使用
  const defaultDescription = "Discover gaming tips, reviews, and insights on the TapixyGames blog. Our gaming experts share their knowledge to help you improve your gaming skills and stay updated with the latest trends.";
  const defaultContent = `
    <div class="space-y-8">
      <article class="border-b border-gray-200 pb-8">
        <h2 class="text-xl font-bold text-[#002b50] mb-2">Top 10 Browser Games You Should Play in 2023</h2>
        <p class="text-gray-500 text-sm mb-3">Published: July 15, 2023</p>
        <p>Browser games have come a long way since the days of simple Flash animations. Today's HTML5 games offer stunning graphics and engaging gameplay without requiring any downloads. In this article, we explore the top 10 browser games you shouldn't miss this year.</p>
        <a href="#" class="inline-block mt-3 text-blue-500 hover:underline">Read more</a>
      </article>
      
      <article class="border-b border-gray-200 pb-8">
        <h2 class="text-xl font-bold text-[#002b50] mb-2">How to Improve Your Gaming Skills: Tips from Pro Gamers</h2>
        <p class="text-gray-500 text-sm mb-3">Published: June 28, 2023</p>
        <p>Whether you're a casual player or aspiring to compete at higher levels, improving your gaming skills takes practice and strategy. We've gathered tips from professional gamers to help you level up your gameplay and achieve better results.</p>
        <a href="#" class="inline-block mt-3 text-blue-500 hover:underline">Read more</a>
      </article>
      
      <article class="border-b border-gray-200 pb-8">
        <h2 class="text-xl font-bold text-[#002b50] mb-2">The Evolution of Online Gaming: From Text Adventures to Multiplayer Worlds</h2>
        <p class="text-gray-500 text-sm mb-3">Published: May 12, 2023</p>
        <p>Online gaming has transformed dramatically over the decades. This article explores the fascinating journey from early text-based adventures to today's immersive multiplayer experiences, highlighting key milestones along the way.</p>
        <a href="#" class="inline-block mt-3 text-blue-500 hover:underline">Read more</a>
      </article>
    </div>
    
    <div class="mt-8 text-center">
      <p>Stay tuned for more gaming articles and updates!</p>
    </div>
  `;

  return (
    <PageLayout>
      <PageContent
        title="Gaming Blogs"
        description={pageData?.description || defaultDescription}
        content={pageData?.hasContent ? pageData.content : defaultContent}
      />
    </PageLayout>
  );
} 