---
description:
globs:
alwaysApply: false
---
# 游戏系统

本项目的核心是游戏系统，包括游戏数据结构、展示和交互逻辑。

## 游戏数据结构

游戏数据定义在[`src/types/game.ts`](mdc:src/types/game.ts)中，主要包含以下接口：

- `Game`: 游戏基础数据结构，包含id、标题、图片等信息
- `GameApiResponse`: API返回的游戏列表数据格式
- `GameDetailResponse`: API返回的游戏详情数据格式
- `GameTag`: 游戏标签数据格式
- `CategoryApiResponse`: 游戏分类API数据格式
- `TagApiResponse`: 标签API数据格式

## 游戏数据源

游戏数据来源有两种方式：

1. 静态数据：[`src/data/games.ts`](mdc:src/data/games.ts)中预定义的游戏数据
2. API请求：通过[`src/lib/api.ts`](mdc:src/lib/api.ts)从API获取游戏数据

## 关键游戏组件

- [`src/components/ui/game-card.tsx`](mdc:src/components/ui/game-card.tsx): 游戏卡片组件
- [`src/components/ui/game-detail.tsx`](mdc:src/components/ui/game-detail.tsx): 游戏详情组件
- [`src/components/ui/game-iframe.tsx`](mdc:src/components/ui/game-iframe.tsx): 游戏iframe组件
- [`src/components/ui/game-section.tsx`](mdc:src/components/ui/game-section.tsx): 游戏板块组件

## 游戏状态管理

游戏系统使用React Context管理状态：

- [`src/contexts/RecentGamesContext.tsx`](mdc:src/contexts/RecentGamesContext.tsx): 最近玩过的游戏
- [`src/contexts/FavoriteGamesContext.tsx`](mdc:src/contexts/FavoriteGamesContext.tsx): 收藏的游戏
- [`src/contexts/LikedGamesContext.tsx`](mdc:src/contexts/LikedGamesContext.tsx): 点赞的游戏

## 游戏路由

- 首页: `/`
- 游戏详情页: `/game/[slug]`
- 游戏分类页: `/category/[slug]`
- 最近游戏: `/recent`
- 收藏游戏: `/favorites`
