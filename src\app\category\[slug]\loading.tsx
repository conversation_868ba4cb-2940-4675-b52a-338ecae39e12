// import { Header } from "@/components/ui/header";
import { Sidebar } from "@/components/ui/sidebar";

export default function CategoryLoading() {
  return (
    <main className="min-h-screen relative bg-primary">
      <Sidebar />

      <div className="ml-16">
        {/* <Header /> */}

        <div className="px-4 sm:px-8 py-6 pt-20 pb-28">
          <div className="mb-8 bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-md">
            <div className="h-10 w-64 bg-gray-200 rounded-lg animate-pulse mb-2"></div>
            <div className="h-5 w-48 bg-gray-200 rounded animate-pulse"></div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 md:gap-5">
            {Array(24).fill(0).map((_, i) => (
              <div key={i} className="w-full aspect-[4/3] max-w-[180px] mx-auto">
                <div className="bg-white rounded-xl overflow-hidden shadow-md animate-pulse h-full">
                  <div className="h-3/4 bg-gray-200"></div>
                  <div className="p-3 h-1/4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex justify-center mt-16 pb-16">
            <div className="flex flex-col items-center space-y-4">
              <div className="flex space-x-2">
                {Array(5).fill(0).map((_, i) => (
                  <div key={i} className="w-10 h-10 rounded-full bg-gray-200 animate-pulse"></div>
                ))}
              </div>
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
} 