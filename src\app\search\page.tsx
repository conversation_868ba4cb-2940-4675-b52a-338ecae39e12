'use client';

import { Suspense } from 'react';
import { SearchResults } from '../../components/ui/search-results';
import { Sidebar } from '../../components/ui/sidebar';
import { RandomGameButton } from '../../components/ui/random-game-button';

function SearchContent() {
  const { useSearchParams } = require('next/navigation');
  const searchParams = useSearchParams();
  const keyword = searchParams.get('keyword') || '';

  return <SearchResults initialKeyword={keyword} />;
}

export default function SearchPage() {
  return (
    <main className="min-h-screen bg-primary relative">
      <Sidebar />
      
      <div className="ml-16">
        <div className="pt-20 pb-28 px-4 sm:px-8">
          <Suspense fallback={
            <div className="flex flex-col items-center justify-center min-h-[300px]">
              <div className="inline-block animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-[#002b50]"></div>
              <p className="mt-4 text-[#002b50] font-medium">Loading search results...</p>
            </div>
          }>
            <SearchContent />
          </Suspense>
        </div>
      </div>
      
      <RandomGameButton />
    </main>
  );
} 