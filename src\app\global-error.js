'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeft } from 'lucide-react';

export default function GlobalError({
  error,
  reset,
}) {
  useEffect(() => {
    console.error('全局错误:', error);
  }, [error]);

  return (
    <html lang="en">
      <body>
        <div className="min-h-screen bg-primary relative flex items-center justify-center">
          <Link href="/"
            className="fixed top-4 left-4 z-50 w-10 h-10 bg-white/80 backdrop-blur rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors"
          >
            <ChevronLeft className="w-6 h-6 text-[#002b50]" />
          </Link>

          <div className="max-w-md w-full mx-auto px-4">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-24 h-24 mx-auto mb-6 relative">
                <svg
                  viewBox="0 0 24 24"
                  className="w-full h-full text-red-500"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z"
                  />
                </svg>
              </div>

              <h1 className="text-xl font-semibold text-[#002b50] mb-4">Oops! Something went wrong</h1>
              <p className="text-gray-600 mb-8">
                We're having a technical issue. Please try again or return to the home page.
              </p>

              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <button
                  onClick={() => reset()}
                  className="px-6 py-3 bg-gradient-to-r from-[#0095ff] to-[#0065ff] text-white font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-100"
                >
                  Try Again
                </button>
                
                <Link
                  href="/"
                  className="px-6 py-3 bg-white border border-[#0065ff] text-[#0065ff] font-medium rounded-full shadow hover:shadow-lg transition-all duration-200 hover:bg-gray-50"
                >
                  Back to Home
                </Link>
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
} 