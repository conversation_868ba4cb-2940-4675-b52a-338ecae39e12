# 项目背景
这是一个使用Next.js和Tailwind CSS开发的小游戏网站

# 语言
这是一个英文小游戏网站

# 项目目标
1. 使用Next.js和Tailwind CSS开发一个简单的小游戏网站
2. 使用Tailwind CSS来实现页面布局

# 要求
1. 符合SEO标准
2. 要适配移动端
3. 不要随意改动已有的样式, 遵循不修改原有功能的原则, 如果功能是新页面的, 那么新开组件和页面, 不要改动原来的组件和页面
4. 不要随意改动已有的API接口,请精准根据要求修改就行, 不要自己随意发挥
5. 不要随意改动已有的游戏数据结构,请精准根据要求修改就行, 不要自己随意发挥

# 设计风格
符合小游戏网站的设计风格, 符合欧美用户审美

# 说明
测试环境下API接口是从 https://tapixyapi.qilaigames.com 获取的

# 网站域名
www.tapixy.com
