export interface Game {
  id: string;
  title: string;
  image: string;
  description?: string;
  url?: string;
  category?: string;
  isHot?: boolean;
  isNew?: boolean;
  isBest?: boolean;
  isEditorPick?: boolean;
  tags?: string[];
  releaseDate?: string;
  developer?: string;
  platform?: string;
  plays?: number;
  rating?: string;
  featured?: boolean;
  
  // Flag fields
  isFree?: boolean;
  isRanked?: boolean;
  
  // Additional fields
  gameType?: string;
  mobile?: boolean;
  videoUrl?: string | null;
  dateAdded?: number;
  game_name?: string;
  
  // Game details
  instructions?: string;
  width?: string;
  height?: string;
  
  // Metrics
  like_count?: number;
  favorite_count?: number;
}

// Game list API response format
export interface GameApiResponse {
  game_id: number;
  name: string;
  description: string;
  image: string;
  file: string;
  category_name: string;
  tags_name: string;
  plays: number;
  rating: string;
  featured: string;
}

// Game detail API response format
export interface GameDetailResponse {
  game_id: number;
  name: string;
  description: string;
  image: string;
  file: string;
  category_name: string;
  plays: number;
  rating: string;
  featured: string;
  catalog_id: string;
  game_name: string;
  instructions: string;
  game_type: string;
  w: number;
  h: number;
  date_added: number;
  published: string;
  mobile: number;
  video_url: string | null;
  tags: GameTag[];
  like_count?: number;
  favorite_count?: number;
}

// Game tag data format
export interface GameTag {
  id: number;
  name: string;
  url: string;
}

// Category API response format
export interface CategoryApiResponse {
  id: number;
  name: string;
  category_pilot: string;
  url: string;
  image?: string;
  description?: string;
  game_count?: number;
}

// Tag API response format
export interface TagApiResponse {
  id: number;
  name: string;
  url: string;
  game_count?: number;
} 