import { Metadata } from 'next';
import { GameSection } from '@/components/ui/game-section';
import { PageHeader } from '@/components/ui/page-header';
import { fetchGamesByTagName } from '@/lib/api';
import { Pagination } from '@/components/ui/pagination';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { BackButton } from '@/components/ui/back-button';

interface TagNamePageProps {
  params: Promise<{
    tagName: string;
  }>;
  searchParams: Promise<{
    page?: string;
  }>;
}

export async function generateMetadata({ params }: TagNamePageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const tagName = decodeURIComponent(resolvedParams.tagName);

  // 生成标签名称相关的关键词
  const tagKeywords = `${tagName}, ${tagName} games, games with ${tagName}, ${tagName} online games`;
  const baseKeywords = 'free games, online games, browser games, no download, instant play';

  return {
    title: `${tagName} Games - Play Free Online Games | TapixyGames`,
    description: `Play the best ${tagName} games online for free on TapixyGames. Enjoy a wide variety of ${tagName} games with no downloads required.`,
    keywords: `${tagKeywords}, ${baseKeywords}`,
    openGraph: {
      title: `${tagName} Games - TapixyGames`,
      description: `Play the best ${tagName} games online for free on TapixyGames.`,
      type: 'website'
    },
  };
}

export default async function TagNamePage({ params, searchParams }: TagNamePageProps) {
  try {
    const resolvedParams = await params;
    console.log('resolvedParams:', resolvedParams);

    // 解码标签名，注意使用全局 decodeURIComponent
    const tagName = decodeURIComponent(resolvedParams.tagName);
    console.log('Decoded tag name:', tagName);

    const resolvedSearchParams = await searchParams;
    console.log('resolvedSearchParams:', resolvedSearchParams);

    const page = resolvedSearchParams?.page ? parseInt(resolvedSearchParams.page) : 1;
    console.log('Using page:', page);

    // 获取标签游戏列表
    console.log('Fetching games for tag name:', tagName);
    const gamesData = await fetchGamesByTagName(tagName, page);

    console.log('Games data received:', {
      gamesCount: gamesData.games?.length || 0,
      totalGames: gamesData.total,
      totalPages: gamesData.totalPages,
      currentPage: gamesData.currentPage
    });

    // 检查游戏数据，必须有非空的games数组
    if (!gamesData.games || gamesData.games.length === 0) {
      console.log('No games found for tag:', tagName);
      if (page === 1) {
        console.log('Returning 404 for first page with no games');
        return notFound();
      }
    }

    // 页面显示逻辑
    return (
      <main className="min-h-screen bg-primary">
        <BackButton href="/" />

        <div className="pt-20 sm:pt-24 pb-10 px-4 sm:px-6 max-w-7xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 mb-6">
            <PageHeader
              title={`${tagName} Games`}
              description={`Discover and play the best ${tagName} games online for free on TapixyGames.`}
            />
          </div>

          {gamesData.games && gamesData.games.length > 0 ? (
            <>
              <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 mb-6">
                <GameSection
                  title={`${tagName} Games`}
                  games={gamesData.games}
                  viewMoreHref=""
                />
              </div>

              {gamesData.totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <Pagination
                    currentPage={gamesData.currentPage}
                    totalPages={gamesData.totalPages}
                    basePath={`/tag/name/${encodeURIComponent(tagName)}`}
                  />
                </div>
              )}
            </>
          ) : (
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4">
                <svg viewBox="0 0 24 24" className="w-full h-full text-[#002b50]" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 12c0-1.232-.046-2.453-.138-3.662a4.006 4.006 0 00-3.7-3.7 48.678 48.678 0 00-7.324 0 4.006 4.006 0 00-3.7 3.7c-.017.22-.032.441-.046.662M19.5 12l3-3m-3 3l-3-3m-12 3c0 1.232.046 2.453.138 3.662a4.006 4.006 0 003.7 3.7 48.656 48.656 0 007.324 0 4.006 4.006 0 003.7-3.7c.017-.22.032-.441.046-.662M4.5 12l3 3m-3-3l-3 3" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-[#002b50] mb-2">No Games Found</h3>
              <p className="text-gray-600 mb-6">There are no games with the "{tagName}" tag.</p>
              <Link
                href="/"
                className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-[#0095ff] to-[#0065ff] text-white font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200 min-w-[200px] hover:scale-105 active:scale-100"
              >
                Back to Home
              </Link>
            </div>
          )}
        </div>
      </main>
    );
  } catch (error) {
    console.error('Error loading games by tag name:', error);
    return notFound();
  }
}