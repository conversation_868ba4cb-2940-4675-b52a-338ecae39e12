'use client';

import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";

interface CategoryPaginationProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
}

export function CategoryPagination({ currentPage, totalPages, baseUrl }: CategoryPaginationProps) {
  // 如果总页数小于1，不显示分页
  if (totalPages <= 1) {
    return null;
  }

  // 创建要显示的页码数组
  const getPageNumbers = () => {
    const pages: (number | "ellipsis")[] = [];
    
    // 始终显示第一页
    pages.push(1);
    
    // 计算中间要显示的页码
    const maxVisiblePages = 5; // 最多显示的页码数量
    let startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);
    
    // 如果页数太少，就从第2页开始
    if (endPage < startPage) {
      endPage = Math.min(totalPages - 1, 2 + maxVisiblePages - 3);
    }
    
    // 如果第一页和开始页之间有间隔，添加省略号
    if (startPage > 2) {
      pages.push("ellipsis");
    }
    
    // 添加中间的页码
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // 如果结束页和最后一页之间有间隔，添加省略号
    if (endPage < totalPages - 1) {
      pages.push("ellipsis");
    }
    
    // 如果总页数大于1，始终显示最后一页
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  const pages = getPageNumbers();

  // 生成页面链接
  const getPageUrl = (page: number) => {
    return `${baseUrl}${page > 1 ? `?page=${page}` : ''}`;
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="flex items-center justify-center space-x-2">
        {/* 上一页按钮 */}
        {currentPage > 1 ? (
          <Link
            href={getPageUrl(currentPage - 1)}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-white shadow-md hover:bg-[#0065ff]/10 transition-colors border border-gray-200"
            aria-label="Previous page"
          >
            <ChevronLeft className="w-5 h-5 text-[#002b50]" />
          </Link>
        ) : (
          <span className="flex items-center justify-center w-10 h-10 rounded-full bg-white/50 opacity-50 cursor-not-allowed">
            <ChevronLeft className="w-5 h-5 text-gray-400" />
          </span>
        )}

        {/* 页码 */}
        {pages.map((page, index) => 
          page === "ellipsis" ? (
            <span key={`ellipsis-${index}`} className="px-3 py-2 text-[#002b50]">
              ...
            </span>
          ) : (
            <Link
              key={page}
              href={getPageUrl(page)}
              className={`flex items-center justify-center w-10 h-10 rounded-full text-sm font-semibold transition-all duration-200 ${
                currentPage === page
                  ? "bg-[#002b50] text-white shadow-lg transform scale-110"
                  : "bg-white text-[#002b50] hover:bg-[#002b50]/10 hover:text-[#002b50] border border-gray-200 shadow-md"
              }`}
            >
              {page}
            </Link>
          )
        )}

        {/* 下一页按钮 */}
        {currentPage < totalPages ? (
          <Link
            href={getPageUrl(currentPage + 1)}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-white shadow-md hover:bg-[#0065ff]/10 transition-colors border border-gray-200"
            aria-label="Next page"
          >
            <ChevronRight className="w-5 h-5 text-[#002b50]" />
          </Link>
        ) : (
          <span className="flex items-center justify-center w-10 h-10 rounded-full bg-white/50 opacity-50 cursor-not-allowed">
            <ChevronRight className="w-5 h-5 text-gray-400" />
          </span>
        )}
      </div>
      
      <div className="text-sm text-[#002b50] font-medium">
        Page {currentPage} of {totalPages}
      </div>
    </div>
  );
} 