'use client';

import Link from 'next/link';
import { ChevronLeft } from 'lucide-react';

export default function Custom404() {
  return (
    <div className="min-h-screen bg-primary relative flex items-center justify-center">
      <Link href="/"
        className="fixed top-4 left-4 z-50 w-10 h-10 bg-white/80 backdrop-blur rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors"
      >
        <ChevronLeft className="w-6 h-6 text-[#002b50]" />
      </Link>

      <div className="max-w-md w-full mx-auto px-4">
        <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
          <div className="w-24 h-24 mx-auto mb-6 relative">
            <svg
              viewBox="0 0 24 24"
              className="w-full h-full text-[#002b50]"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"
              />
            </svg>
          </div>

          <h1 className="text-4xl font-bold text-[#002b50] mb-2">404</h1>
          <h2 className="text-xl font-semibold text-[#002b50] mb-4">Page Not Found</h2>
          <p className="text-gray-600 mb-8">
            We can't find the page you're looking for. It might have been removed or doesn't exist.
          </p>

          <Link
            href="/"
            className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-[#0095ff] to-[#0065ff] text-white font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200 min-w-[200px] hover:scale-105 active:scale-100"
          >
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
} 