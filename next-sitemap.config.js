/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://www.tapixy.com',
  generateRobotsTxt: true, // 生成robots.txt
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
      },
      {
        userAgent: '*',
        disallow: ['/admin/', '/api/', '/private/'],
      },
    ],
    additionalSitemaps: [
      // 暂时注释掉，直到API问题解决
      // 'https://www.tapixy.com/api/sitemap-games',
      'https://www.tapixy.com/api/sitemap-categories',
    ],
  },
  exclude: ['/admin/*', '/api/*', '/private/*'],
  // 修改为自动更新频率
  changefreq: 'daily',
  priority: 0.7,
  sitemapSize: 7000,
  // 添加动态游戏页面到sitemap
  additionalPaths: async (config) => {
    // 可以在这里添加额外的动态路由
    return []
  },
} 