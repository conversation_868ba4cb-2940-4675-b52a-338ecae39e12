import Link from "next/link";
import { notFound } from "next/navigation";
import { Metadata } from "next";
// import { Header } from "@/components/ui/header";
import { Sidebar } from "@/components/ui/sidebar";
import { GameCard } from "@/components/ui/game-card";
import { RandomGameButton } from "@/components/ui/random-game-button";
import { CategoryPagination } from "@/components/ui/category-pagination";
import { BackButton } from "@/components/ui/back-button";
import {
  fetchLatestGames,
  fetchPopularGames,
  fetchFeaturedGames,
  fetchTopRatedGames,
  fetchGamesByCategory,
  fetchAllCategories
} from "@/lib/api";
import { Game, CategoryApiResponse } from "@/types/game";

// Revalidate the page every 1 hour (3600 seconds)
export const revalidate = 3600;

type Props = {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

// 生成页面元数据
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // 不再需要await params
  const slug = params.slug;

  // 使用generateStaticParams中声明的类似逻辑获取分类标题
  const categoryTitle = getCategoryTitle(slug);

  // 生成分类相关的关键词
  const categoryKeywords = `${categoryTitle.toLowerCase()}, ${categoryTitle.toLowerCase()} games, free ${categoryTitle.toLowerCase()} games`;
  const baseKeywords = 'free games, online games, browser games, no download, instant play';

  return {
    title: `${categoryTitle} - Play Free Online Games | TapixyGames`,
    description: `Play the best ${categoryTitle} online for free. No downloads, no sign-ups, just instant fun browser games. Enjoy a wide selection of ${categoryTitle.toLowerCase()} games.`,
    keywords: `${categoryKeywords}, ${baseKeywords}`,
    openGraph: {
      title: `${categoryTitle} - Play Free Online Games | TapixyGames`,
      description: `Play the best ${categoryTitle} online for free. No downloads, no sign-ups, just instant fun browser games.`,
      type: 'website',
    },
  };
}

// Generate static parameters
export async function generateStaticParams() {
  // Try to fetch all categories
  try {
    const categories: CategoryApiResponse[] = await fetchAllCategories();
    const specialCategories = ['latest', 'popular', 'featured', 'top-rated'];

    // Combine special categories and API-returned categories
    return [
      ...specialCategories.map(slug => ({ slug })),
      ...categories.map((category: CategoryApiResponse) => ({ 
        slug: category.category_pilot.toLowerCase()
      }))
    ];
  } catch (error) {
    // If category fetching fails, return special categories
    console.error('Failed to fetch categories for static generation:', error);
    return [
      { slug: 'latest' },
      { slug: 'popular' },
      { slug: 'featured' },
      { slug: 'top-rated' }
    ];
  }
}

// Get category display title
function getCategoryTitle(slug: string): string {
  const titleMap: Record<string, string> = {
    'latest': 'Latest Games',
    'popular': 'Popular Games',
    'featured': 'Featured Games',
    'top-rated': 'Top Rated Games',
  };

  // 如果是特殊类别，直接返回映射的标题
  if (titleMap[slug]) {
    return titleMap[slug];
  }

  // 对URL编码的分类名进行解码
  const decodedSlug = decodeURIComponent(slug);

  // 将slug中的连字符转为空格，并将首字母大写
  return decodedSlug
    .split("-")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

// 默认游戏数据，用于API请求失败时
const defaultGames: Game[] = [
  {
    id: "fruit-ninja",
    title: "Fruit Ninja",
    image: "https://ext.same-assets.com/2873387825/1062497655.jpeg",
    isHot: true,
    category: "arcade"
  },
  {
    id: "angry-birds",
    title: "Angry Birds",
    image: "https://ext.same-assets.com/2873387825/4182098242.jpeg",
    isHot: true,
    category: "puzzle"
  },
  {
    id: "candy-crush",
    title: "Candy Crush",
    image: "https://ext.same-assets.com/2873387825/2127849706.jpeg",
    isHot: true,
    category: "puzzle"
  },
  {
    id: "among-us",
    title: "Among Us",
    image: "https://ext.same-assets.com/2873387825/3987655339.jpeg",
    isNew: true,
    category: "social"
  }
];

export default async function CategoryPage({ params, searchParams }: Props) {
  // 不再需要await params
  const slug = params.slug;

  // 不再需要await searchParams
  const currentPage = parseInt(searchParams.page as string || '1', 10);
  const pageLimit = parseInt(searchParams.limit as string || '24', 10);

  // 解码分类名，处理带有空格等特殊字符的分类
  const decodedSlug = decodeURIComponent(slug);

  // 分页和游戏数据
  let categoryGames: Game[] = [];
  let totalGames = 0;
  let totalPages = 1;

  try {
    switch (slug) {
      case 'latest': {
        const result = await fetchLatestGames(currentPage, pageLimit);
        categoryGames = result.games || [];
        totalGames = result.total || categoryGames.length;
        totalPages = result.totalPages || Math.ceil(totalGames / pageLimit);
        break;
      }
      case 'popular': {
        const result = await fetchPopularGames(currentPage, pageLimit);
        categoryGames = result.games || [];
        totalGames = result.total || categoryGames.length;
        totalPages = result.totalPages || Math.ceil(totalGames / pageLimit);
        break;
      }
      case 'featured': {
        const result = await fetchFeaturedGames(currentPage, pageLimit);
        categoryGames = result.games || [];
        totalGames = result.total || categoryGames.length;
        totalPages = result.totalPages || Math.ceil(totalGames / pageLimit);
        break;
      }
      case 'top-rated': {
        const result = await fetchTopRatedGames(currentPage, pageLimit);
        categoryGames = result.games || [];
        totalGames = result.total || categoryGames.length;
        totalPages = result.totalPages || Math.ceil(totalGames / pageLimit);
        break;
      }
      default:
        try {
          // Try to get category games by category name, use decoded slug
          const result = await fetchGamesByCategory(decodedSlug, currentPage, pageLimit);
          categoryGames = result.games || [];
          totalGames = result.total || categoryGames.length;
          totalPages = result.totalPages || Math.ceil(totalGames / pageLimit);
          console.log(`Fetched ${categoryGames.length} games for category ${decodedSlug}`);
        } catch (categoryError) {
          console.error(`Failed to fetch games for category ${decodedSlug}:`, categoryError);
          categoryGames = [];
        }
    }
  } catch (error) {
    console.error(`Failed to fetch games for category ${decodedSlug}:`, error);
    categoryGames = [];
  }

  // 如果没有游戏，尝试使用默认游戏，按分类过滤
  if (!categoryGames || categoryGames.length === 0) {
    console.log(`No games found for category: ${decodedSlug}, using default games`);
    // 简单过滤，如果分类名包含关键词，就返回相应的游戏
    const categoryLower = decodedSlug.toLowerCase();
    categoryGames = defaultGames.filter(game =>
      game.category && game.category.toLowerCase().includes(categoryLower.split(' ')[0])
    );

    // 如果过滤后仍然没有游戏，返回所有默认游戏
    if (categoryGames.length === 0) {
      categoryGames = defaultGames;
    }

    totalGames = categoryGames.length;
    totalPages = Math.ceil(totalGames / pageLimit);
  }

  const categoryTitle = getCategoryTitle(slug);
  const baseUrl = `/category/${slug}`;

  return (
    <main className="min-h-screen relative bg-primary">
      <Sidebar />
      <BackButton href="/" />

      <div className="ml-16">
        {/* <Header /> */}

        <div className="px-4 sm:px-8 py-6 pt-20 pb-28">
          <div className="mb-8 bg-white/70 backdrop-blur-sm p-6 rounded-2xl shadow-md">
            <h1 className="text-3xl font-bold text-[#002b50]">{categoryTitle}</h1>
            <p className="text-[#002b50] mt-2">
              {totalGames > 0
                ? `Showing ${Math.min((currentPage - 1) * pageLimit + 1, totalGames)}-${Math.min(currentPage * pageLimit, totalGames)} of ${totalGames} games`
                : 'No games found'}
            </p>
          </div>

          {categoryGames.length > 0 ? (
            <>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 sm:gap-4 md:gap-5">
                {categoryGames.map(game => (
                  <div key={game.id} className="w-full aspect-[4/3] max-w-[180px] mx-auto">
                    <GameCard
                      imageUrl={game.image}
                      title={game.title}
                      href={`/game/${game.id}`}
                      isHot={game.isHot}
                      isNew={game.isNew}
                      isBest={game.isBest}
                      isEditorPick={game.isEditorPick}
                    />
                  </div>
                ))}
              </div>

              <div className="mt-16 pb-16">
                <CategoryPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  baseUrl={baseUrl}
                />
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <h2 className="text-2xl font-bold text-primary mb-4">No Games Found</h2>
              <p className="text-primary/80 mb-8">
                We couldn't find any games in this category. Please try another category or check back later.
              </p>
              <Link
                href="/"
                className="inline-flex items-center justify-center px-6 py-3 bg-white hover:bg-gray-100 text-primary font-medium rounded-full transition-colors"
              >
                <svg viewBox="0 0 24 24" className="w-5 h-5 mr-2" fill="currentColor">
                  <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
                Return Home
              </Link>
            </div>
          )}
        </div>
      </div>

      <RandomGameButton />
    </main>
  );
}
