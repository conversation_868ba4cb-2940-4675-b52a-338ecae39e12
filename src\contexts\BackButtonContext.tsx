'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

type BackButtonContextType = {
  hasBackButton: boolean;
  setHasBackButton: (value: boolean) => void;
};

const BackButtonContext = createContext<BackButtonContextType | undefined>(undefined);

export function BackButtonProvider({ children }: { children: ReactNode }) {
  const [hasBackButton, setHasBackButton] = useState(false);

  return (
    <BackButtonContext.Provider value={{ hasBackButton, setHasBackButton }}>
      {children}
    </BackButtonContext.Provider>
  );
}

export function useBackButton() {
  const context = useContext(BackButtonContext);
  if (context === undefined) {
    throw new Error('useBackButton must be used within a BackButtonProvider');
  }
  return context;
} 