# CentOS 7 部署和启动说明

本项目可以在CentOS 7系统上使用Docker进行部署和运行。由于CentOS 7可能不支持最新版本的Node.js，使用Docker是最佳解决方案。

## 前置条件

确保您的CentOS 7服务器已安装以下软件：

- Git
- Docker
- Docker Compose

如果尚未安装，可以使用以下命令安装：

```bash
# 安装Git
sudo yum install -y git

# 安装Docker
sudo yum install -y yum-utils device-mapper-persistent-data lvm2
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.16.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 部署和启动步骤

### 1. 克隆仓库（如果是首次部署）

```bash
git clone https://your-repository-url.git
cd flygames-web
```

### 2. 更新代码

每次部署前，请确保先获取最新代码：

```bash
git pull
```

### 3. 使用Docker Compose启动应用

```bash
docker-compose up -d
```

这将在后台启动应用程序。`-d`参数表示以"分离模式"运行容器。

### 4. 验证应用是否成功运行

```bash
docker-compose ps
```

您应该能看到应用状态为"Up"。

## 常用命令

- 查看应用日志：
  ```bash
  docker-compose logs -f
  ```

- 停止应用：
  ```bash
  docker-compose down
  ```

- 重启应用：
  ```bash
  docker-compose restart
  ```

- 重新构建并启动（代码有更新时）：
  ```bash
  git pull
  docker-compose up -d --build
  ```

## 注意事项

- 确保服务器防火墙允许应用使用的端口（默认为3000）
- 如需修改端口配置，请编辑docker-compose.yml文件
- 确保服务器有足够的磁盘空间用于Docker镜像和容器 