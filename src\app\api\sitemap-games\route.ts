import { NextResponse } from 'next/server';

// 获取所有游戏数据
async function fetchAllGames() {
  try {
    const allGames = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      console.log(`Fetching games page ${page}...`);
      const response = await fetch(`https://tapixyapi.qilaigames.com/api/games?page=${page}&limit=100`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      console.log(`Page ${page} data:`, {
        gamesCount: data.games?.length || 0,
        currentPage: data.currentPage,
        totalPages: data.totalPages
      });
      
      if (data.games && data.games.length > 0) {
        allGames.push(...data.games);
        
        // 检查是否还有更多页
        if (data.currentPage < data.totalPages) {
          page++;
        } else {
          hasMore = false;
        }
      } else {
        hasMore = false;
      }
    }
    
    console.log(`Total games fetched: ${allGames.length}`);
    return allGames;
  } catch (error) {
    console.error('Error fetching games for sitemap:', error);
    return [];
  }
}

// 生成游戏URL
function getGameUrl(game: any) {
  try {
    // 优先使用自定义URL
    if (game.customUrl) {
      return `https://www.tapixy.com/game/${encodeURIComponent(game.customUrl)}`;
    }
    
    // 如果没有自定义URL，使用ID
    return `https://www.tapixy.com/game/${encodeURIComponent(game.id)}`;
  } catch (error) {
    console.error('Error generating game URL:', error, game);
    return null;
  }
}

export async function GET() {
  try {
    const games = await fetchAllGames();
    
    // 如果没有获取到游戏数据，返回404
    if (!games || games.length === 0) {
      console.log('No games found for sitemap');
      return new NextResponse('No games found', { status: 404 });
    }
    
    // 过滤掉无效的URL
    const validGames = games.filter(game => {
      const url = getGameUrl(game);
      return url !== null;
    });
    
    // 如果没有有效的游戏URL，返回404
    if (validGames.length === 0) {
      console.log('No valid game URLs found');
      return new NextResponse('No valid game URLs found', { status: 404 });
    }
    
    // 生成XML
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${validGames.map((game: any) => {
  const url = getGameUrl(game);
  return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
}).join('\n')}
</urlset>`;
    
    // 返回XML响应
    return new NextResponse(xml.trim(), {
      headers: {
        'Content-Type': 'application/xml',
      },
    });
  } catch (error) {
    console.error('Error generating sitemap:', error);
    return new NextResponse('Error generating sitemap', { status: 500 });
  }
}

// 对于动态内容，不使用缓存
export const dynamic = 'force-dynamic'; 