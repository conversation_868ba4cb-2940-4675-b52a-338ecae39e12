'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useBackButton } from '@/contexts/BackButtonContext';

export function Header() {
  const router = useRouter();
  const [keyword, setKeyword] = useState('');
  const { hasBackButton } = useBackButton();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (keyword.trim()) {
      router.push(`/search?keyword=${encodeURIComponent(keyword.trim())}`);
    }
  };

  const handleSearchIconClick = () => {
    if (keyword.trim()) {
      router.push(`/search?keyword=${encodeURIComponent(keyword.trim())}`);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 w-full flex justify-between items-center py-2 sm:py-2 bg-primary z-50 px-2 sm:px-4">
      <div className="flex items-center gap-2 sm:gap-4 w-full">
        {/* FL Logo - 当页面有返回按钮时隐藏 */}
        {!hasBackButton && (
          <div className="w-10 sm:w-12 flex justify-center items-center sm:justify-start">
            <Link href="/" className="flex items-center">
              <div className="flex flex-col items-center leading-none">
                <div className="flex items-center">
                  <span className="text-base sm:text-lg font-bold text-white">T</span>
                  <span className="text-base sm:text-lg font-bold text-white">A</span>
                  <span className="text-base sm:text-lg font-bold text-white">P</span>
                </div>
                <div className="flex items-center -mt-2">
                  <span className="text-base sm:text-lg font-bold text-black">I</span>
                  <span className="text-base sm:text-lg font-bold text-black">X</span>
                  <span className="text-base sm:text-lg font-bold text-black">Y</span>
                </div>
              </div>
            </Link>
          </div>
        )}

        {/* 如果有返回按钮，添加一个空间占位符 */}
        {hasBackButton && <div className="w-10 sm:w-12" />}

        {/* 搜索框 - 在小屏幕上调整大小和位置 */}
        <div className="flex-1 max-w-xl mr-4 relative">
          <form onSubmit={handleSearch} className="relative">
            <input
              type="text"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              placeholder="Search games..."
              className="w-full h-10 pl-4 pr-12 rounded-full bg-white/90 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 placeholder-gray-500"
            />
            <button
              type="submit"
              onClick={handleSearchIconClick}
              className="absolute right-0 top-0 h-full w-12 flex items-center justify-center text-gray-500 hover:text-gray-700"
            >
              <Search className="w-5 h-5" />
            </button>
          </form>
        </div>

        {/* 右侧按钮 - 在小屏幕上也显示 */}
        <div className="flex items-center gap-2 sm:gap-4 ml-auto">
          <Link
            href="/recent"
            className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-white flex items-center justify-center hover:bg-gray-100"
          >
            <svg viewBox="0 0 24 24" width="16" height="16" className="fill-current text-gray-500 sm:w-5 sm:h-5">
              <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
            </svg>
          </Link>

          <Link
            href="/favorites"
            className="w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-white flex items-center justify-center hover:bg-gray-100"
          >
            <svg viewBox="0 0 24 24" width="16" height="16" className="fill-current text-gray-500 sm:w-5 sm:h-5">
              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
            </svg>
          </Link>
        </div>
      </div>
    </header>
  );
}
