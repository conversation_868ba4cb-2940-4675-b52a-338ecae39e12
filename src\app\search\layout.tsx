import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Search Games - Find Your Favorite Games | TapixyGames',
  description: 'Search and discover your favorite free online games on TapixyGames. Find games by name, category, or type and play instantly in your browser.',
  keywords: 'game search, find games, search online games, free games search, browser games search',
  openGraph: {
    title: 'Search Games - TapixyGames',
    description: 'Search and discover your favorite free online games on TapixyGames. Find games by name, category, or type and play instantly in your browser.',
  },
};

export default function SearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
