import { fetchGamesByTag } from '@/lib/api';
import { GameSection } from '@/components/ui/game-section';
import { PageHeader } from '@/components/ui/page-header';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

interface TagPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: TagPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const tagId = resolvedParams.slug;

  // 生成标签相关的关键词
  const tagKeywords = `${tagId}, ${tagId} games, games with ${tagId}`;
  const baseKeywords = 'free games, online games, browser games, no download, instant play';

  return {
    title: `Games Tagged ${tagId} - Play Free Online Games | TapixyGames`,
    description: `Play the best games tagged as ${tagId} online for free on TapixyGames. Discover a variety of ${tagId} games with no downloads required.`,
    keywords: `${tagKeywords}, ${baseKeywords}`,
    openGraph: {
      title: `Games Tagged ${tagId} - TapixyGames`,
      description: `Play the best games tagged as ${tagId} online for free on TapixyGames.`,
      type: 'website'
    },
  };
}

export default async function TagPage({ params }: TagPageProps) {
  const resolvedParams = await params;
  const tagId = resolvedParams.slug;

  // 获取标签游戏列表
  const tagGames = await fetchGamesByTag(tagId);

  if (!tagGames.length) {
    notFound();
  }

  return (
    <main className="min-h-screen pt-20 sm:pt-24 pb-10 px-4 sm:px-6">
      <div className="max-w-7xl mx-auto">
        <PageHeader
          title={`Games Tagged "${tagId}"`}
          description={`Discover and play the best games tagged as ${tagId} online for free on TapixyGames.`}
        />

        <div className="mt-8">
          <GameSection
            title={`${tagId} Games`}
            games={tagGames}
            viewMoreHref=""
          />
        </div>
      </div>
    </main>
  );
}