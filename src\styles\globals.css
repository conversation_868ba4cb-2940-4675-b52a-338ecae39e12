@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 主题色变量 */
  --primary-bg: #fae962;
  --primary-text: #002b50;
  --primary-hover: #f5e34d;
  --primary-active: #29b9ff;
  
  /* 原有变量保持不变 */
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 233, 98;
  --background-end-rgb: 255, 255, 255;
}

/* 添加主题类 */
@layer components {
  .bg-primary {
    background-color: var(--primary-bg);
  }
  
  .text-primary {
    color: var(--primary-text);
  }
  
  .hover-primary:hover {
    background-color: var(--primary-hover);
  }
  
  .active-primary {
    background-color: var(--primary-active);
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer base {
  html {
    scroll-behavior: smooth;
  }
}

/* 为游戏卡片设置统一高度 */
.game-card {
  transition: transform 0.3s ease;
}

.game-card:hover {
  transform: translateY(-5px);
}

/* 侧边栏动画 */
.sidebar-transition {
  transition: width 0.3s ease-in-out;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
} 