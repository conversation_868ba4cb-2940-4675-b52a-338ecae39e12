import { Metadata } from 'next';
import { GameDetail } from '@/components/ui/game-detail';
import { fetchGameByName, fetchRecommendedGames, fetchGame } from '@/lib/api';
import { notFound, redirect } from 'next/navigation';
import { formatSlug } from '@/lib/url-utils';

type Props = {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // 等待解析params对象
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  if (!slug) {
    return {
      title: 'Game Not Found - TapixyGames',
      description: 'The game you are looking for could not be found.',
    };
  }

  try {
    // 检查slug是否可能是ID（只包含数字）
    const isPossibleId = /^\d+$/.test(slug);
    let game;

    if (isPossibleId) {
      game = await fetchGame(slug);
    } else {
      game = await fetchGameByName(slug);
    }

    if (!game) {
      return {
        title: 'Game Not Found - TapixyGames',
        description: 'The game you are looking for could not be found.',
      };
    }

    // 从游戏类别和标签生成关键词
    const categoryKeywords = game.category ? `${game.category}, ${game.category} games` : '';
    const tagKeywords = game.tags && game.tags.length > 0 ? game.tags.join(', ') : '';
    const baseKeywords = 'free online games, browser games, no download';
    const keywords = [game.title, categoryKeywords, tagKeywords, baseKeywords].filter(Boolean).join(', ');

    return {
      title: `${game.title} - Play Free Online Games | TapixyGames`,
      description: game.description?.slice(0, 160) || `Play ${game.title} online for free on TapixyGames. No downloads required, play instantly in your browser!`,
      keywords: keywords,
      openGraph: {
        title: `${game.title} - TapixyGames`,
        description: game.description?.slice(0, 160) || `Play ${game.title} online for free`,
        images: [game.image],
        type: 'website'
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Game Not Found - TapixyGames',
      description: 'The game you are looking for could not be found.',
    };
  }
}

export default async function GamePage({ params }: Props) {
  // 等待解析params对象
  const resolvedParams = await params;
  const slug = resolvedParams.slug;

  if (!slug) {
    notFound();
  }

  // 检查slug是否可能是ID（只包含数字）
  const isPossibleId = /^\d+$/.test(slug);
  let game;

  if (isPossibleId) {
    game = await fetchGame(slug);
    if (game) {
      // 如果是ID并且找到了游戏，重定向到基于game_name的URL
      if (game.game_name) {
        redirect(`/game/${encodeURIComponent(game.game_name.toLowerCase().replace(/\s+/g, '-'))}`);
      } else if (game.title) {
        // 如果没有game_name，使用title
        redirect(`/game/${encodeURIComponent(game.title.toLowerCase().replace(/\s+/g, '-'))}`);
      }
    }
  } else {
    game = await fetchGameByName(slug);
  }

  // 如果没有找到游戏，返回404
  if (!game) {
    notFound();
  }
  
  // 调试：打印游戏数据
  console.log('Game data:', {
    id: game.id,
    title: game.title,
    like_count: game.like_count,
    favorite_count: game.favorite_count,
  });

  // 获取推荐游戏
  const recommendedGamesData = await fetchRecommendedGames(game.id).catch(() => []);

  // 处理推荐游戏数据，确保得到一个数组
  const recommendedGames = Array.isArray(recommendedGamesData)
    ? recommendedGamesData
    : [];

  return (
    <main className="min-h-screen bg-primary">
      <GameDetail game={game} recommendedGames={recommendedGames} />
    </main>
  );
}