import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 百度统计工具函数
 */
declare global {
  interface Window {
    _hmt: any[];
  }
}

/**
 * 手动触发百度统计页面访问
 * @param url 页面URL，如果不提供则使用当前页面URL
 */
export function trackPageView(url?: string) {
  if (typeof window !== 'undefined' && window._hmt) {
    const trackUrl = url || window.location.pathname + window.location.search;
    window._hmt.push(['_trackPageview', trackUrl]);
    console.log('百度统计 - 页面访问:', trackUrl);
  }
}

/**
 * 跟踪自定义事件
 * @param category 事件类别
 * @param action 事件动作
 * @param label 事件标签（可选）
 * @param value 事件值（可选）
 */
export function trackEvent(category: string, action: string, label?: string, value?: number) {
  if (typeof window !== 'undefined' && window._hmt) {
    const eventData = ['_trackEvent', category, action];
    if (label) eventData.push(label);
    if (value !== undefined) eventData.push(value);

    window._hmt.push(eventData);
    console.log('百度统计 - 事件跟踪:', { category, action, label, value });
  }
}
