'use client';

import React, { useMemo } from 'react';
import Link from 'next/link';
import { GameCard } from './game-card';
import { Game } from '@/types/game';
import { getGameDetailUrl } from '@/lib/url-utils';

interface GameSectionProps {
  title: string;
  games: Game[];
  viewMoreHref: string;
}

export function GameSection({ title, games, viewMoreHref }: GameSectionProps) {
  // 安全检查，确保games是数组
  const gameArray = Array.isArray(games) ? games : [];
  
  // 使用标题的字符串长度作为种子来选择固定的索引
  // 这样对于同一个标题，每次渲染都会选择相同的索引
  const largeIndex = useMemo(() => {
    if (gameArray.length === 0) return 0;
    const seed = title.length;
    return seed % Math.min(4, gameArray.length);
  }, [title, gameArray.length]);
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-gray-900">{title}</h2>
        {viewMoreHref && (
          <Link 
            href={viewMoreHref}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium hidden sm:block"
          >
            View More
          </Link>
        )}
      </div>

      {/* 移动端布局 */}
      <div className="grid grid-cols-3 gap-3 sm:hidden">
        {gameArray.map((game, index) => (
          <div key={game.id} className={index === 0 ? 'col-span-2 row-span-2' : ''}>
            <GameCard
              imageUrl={game.image}
              title={game.title}
              href={`/game/${game.game_name}`}
              isHot={index < 5 ? game.isHot : false}
              isNew={index < 5 ? game.isNew : false}
              isBest={game.isBest}
              isEditorPick={game.isEditorPick}
            />
          </div>
        ))}
      </div>

      {/* 桌面端布局 */}
      <div className="hidden sm:grid grid-cols-8 gap-4">
        {gameArray.map((game, index) => (
          <div key={game.id} className={`${index === largeIndex ? 'col-span-2 row-span-2' : ''}`}>
            <GameCard
              imageUrl={game.image}
              title={game.title}
              href={getGameDetailUrl(game)}
              isHot={index < 5 ? game.isHot : false}
              isNew={index < 5 ? game.isNew : false}
              isBest={game.isBest}
              isEditorPick={game.isEditorPick}
              isLarge={index === largeIndex}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
