import './globals.css'
import { Metada<PERSON> } from "next";
import { Noto_Sans_SC } from "next/font/google";
import ClientRootLayout from "@/components/layouts/client-root-layout";
import { Inter } from 'next/font/google';
import { Header } from '@/components/ui/header';
import { FavoriteGamesProvider } from '@/contexts/FavoriteGamesContext';
import { LikedGamesProvider } from '@/contexts/LikedGamesContext';
import { RecentGamesProvider } from '@/contexts/RecentGamesContext';
import { RandomGameButton } from '@/components/ui/random-game-button';
import { RouteTracker } from '@/components/analytics/route-tracker';
import { AnalyticsProvider } from '@/components/analytics/analytics-wrapper';
import Script from 'next/script';

// Use Google Font: Noto Sans SC (supports Chinese characters too)
const notoSansSC = Noto_Sans_SC({
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  display: "swap",
});

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'TapixyGames - Play Free Online Games',
  description: 'Play thousands of free online games on TapixyGames! We have a huge collection of action, puzzle, racing, shooting, and many more fun games.',
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="overflow-x-hidden">
        {/* 百度统计代码 */}
        <Script
          id="baidu-analytics"
          strategy="afterInteractive"
        >
          {`
            var _hmt = _hmt || [];
            (function() {
              var hm = document.createElement("script");
              hm.src = "https://hm.baidu.com/hm.js?aa8db1c32bdcf8c909930e2d6afec821";
              var s = document.getElementsByTagName("script")[0];
              s.parentNode.insertBefore(hm, s);
            })();
          `}
        </Script>
        <ClientRootLayout>
          <AnalyticsProvider>
            <FavoriteGamesProvider>
              <LikedGamesProvider>
                <RecentGamesProvider>
                  <Header />
                  <main className="min-h-screen bg-gray-50">
                    {children}
                  </main>
                  <RandomGameButton />
                  {/* 路由跟踪组件 - 自动发送百度统计 */}
                  <RouteTracker />
                </RecentGamesProvider>
              </LikedGamesProvider>
            </FavoriteGamesProvider>
          </AnalyticsProvider>
        </ClientRootLayout>
      </body>
    </html>
  );
}
