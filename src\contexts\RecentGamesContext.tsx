'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { Game } from '@/types/game';

interface RecentGamesContextType {
  recentGames: Game[];
  addRecentGame: (game: Game) => void;
  clearRecentGames: () => void;
}

const RECENT_GAMES_KEY = 'recent_games';
const MAX_RECENT_GAMES = 10;

const RecentGamesContext = createContext<RecentGamesContextType>({
  recentGames: [],
  addRecentGame: () => {},
  clearRecentGames: () => {}
});

export function RecentGamesProvider({ children }: { children: React.ReactNode }) {
  const [recentGames, setRecentGames] = useState<Game[]>([]);

  // 加载最近游戏
  useEffect(() => {
    try {
      const stored = localStorage.getItem(RECENT_GAMES_KEY);
      if (stored) {
        setRecentGames(JSON.parse(stored));
      }
    } catch (error) {
      console.error('Error loading recent games:', error);
    }
  }, []);

  // 添加游戏到最近列表
  const addRecentGame = (game: Game) => {
    setRecentGames(prev => {
      // 移除重复的游戏
      const filtered = prev.filter(g => g.id !== game.id);
      // 添加到开头
      const updated = [game, ...filtered].slice(0, MAX_RECENT_GAMES);
      // 保存到本地存储
      try {
        localStorage.setItem(RECENT_GAMES_KEY, JSON.stringify(updated));
      } catch (error) {
        console.error('Error saving recent games:', error);
      }
      return updated;
    });
  };

  // 清除最近游戏列表
  const clearRecentGames = () => {
    try {
      localStorage.removeItem(RECENT_GAMES_KEY);
    } catch (error) {
      console.error('Error clearing recent games:', error);
    }
    setRecentGames([]);
  };

  return (
    <RecentGamesContext.Provider
      value={{
        recentGames,
        addRecentGame,
        clearRecentGames
      }}
    >
      {children}
    </RecentGamesContext.Provider>
  );
}

export function useRecentGames() {
  const context = useContext(RecentGamesContext);
  if (!context) {
    throw new Error('useRecentGames must be used within a RecentGamesProvider');
  }
  return context;
} 