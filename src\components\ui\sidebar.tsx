'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { getCategories } from '@/lib/api';

// 固定的菜单项
const fixedMenuItems = [
  {
    icon: "/images/icons/home.gif",
    alt: "Home",
    href: "/"
  },
  {
    icon: "/images/icons/category.gif",
    alt: "Categories",
    href: "/categories"
  },
  {
    icon: "/images/icons/new.gif",
    alt: "New Games",
    href: "/#new-games"
  },
  {
    icon: "/images/icons/hot.gif",
    alt: "Hot Games",
    href: "/#hot-games"
  }
];

interface SidebarProps {
  onStateChange?: (isCollapsed: boolean) => void;
}

interface SectionPosition {
  href: string;
  top: number;
  bottom: number;
}

interface Category {
  name: string;
  category_pilot: string;
  image: string;
  show_home: number;
}

export function Sidebar({ onStateChange }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [activeItem, setActiveItem] = useState<string>("/");
  const pathname = usePathname();
  const [categories, setCategories] = useState<Category[]>([]);

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      const data = await getCategories();
      // 只显示 show_home 为 1 的分类
      setCategories(data.filter(cat => cat.show_home === 1));
    };
    fetchCategories();
  }, []);

  // 监听滚动事件，更新激活状态
  useEffect(() => {
    if (pathname !== '/') return; // 只在首页启用滚动监听

    const handleScroll = () => {
      const sections = [
        { id: 'new-games', href: '/#new-games' },
        { id: 'hot-games', href: '/#hot-games' },
        ...categories.map(cat => ({
          id: cat.category_pilot.toLowerCase(),
          href: `/category/${cat.category_pilot.toLowerCase()}`
        }))
      ];

      // 获取所有section的位置
      const sectionPositions: SectionPosition[] = sections.map(section => {
        const element = document.getElementById(section.id);
        if (!element) return { href: section.href, top: Infinity, bottom: Infinity };
        const rect = element.getBoundingClientRect();
        return {
          href: section.href,
          top: rect.top,
          bottom: rect.bottom
        };
      }).filter(section => section.top !== Infinity);

      // 找到当前在视窗中的section
      const viewportHeight = window.innerHeight;
      const currentSection = sectionPositions.find(section => {
        return (section.top >= 0 && section.top <= viewportHeight * 0.5) || 
               (section.bottom >= 0 && section.bottom <= viewportHeight) ||
               (section.top <= 0 && section.bottom >= viewportHeight);
      }) || { href: '/', top: 0, bottom: 0 };

      setActiveItem(currentSection.href);
    };

    // 添加滚动监听，使用 requestAnimationFrame 优化性能
    let ticking = false;
    const scrollListener = () => {
      if (!ticking) {
        window.requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', scrollListener);
    // 初始调用一次以设置初始状态
    handleScroll();

    return () => {
      window.removeEventListener('scroll', scrollListener);
    };
  }, [pathname, categories]);

  // 监听窗口高度和宽度变化并调整间距
  useEffect(() => {
    const checkScreen = () => {
      setIsSmallScreen(window.innerHeight < 700 || window.innerWidth < 640);
    };
    
    // 初始检查
    checkScreen();
    
    // 添加窗口大小变化事件监听
    window.addEventListener('resize', checkScreen);
    
    // 清理事件监听
    return () => window.removeEventListener('resize', checkScreen);
  }, []);

  // 当侧边栏状态变化时通知父组件
  useEffect(() => {
    if (onStateChange) {
      onStateChange(isCollapsed);
    }
  }, [isCollapsed, onStateChange]);

  // 添加滚动到指定区域的函数，带偏移量
  const scrollToSection = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    // 如果不是首页，且链接是指向首页的hash或分类，则允许正常跳转
    if (pathname !== '/' && (href.startsWith('/#') || href.startsWith('/category/'))) {
      setActiveItem(href);
      return;
    }
    
    // 如果是首页上的hash链接，阻止默认行为进行平滑滚动
    if (pathname === '/' && href.startsWith('/#')) {
      e.preventDefault();
      setActiveItem(href);
      
      const hashId = href.substring(2); // 去掉'/#'前缀
      const targetElement = document.getElementById(hashId);
      
      if (targetElement) {
        const offset = 80; // 头部高度约为 72px，额外留出一些空间
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
      return;
    }

    // 如果是分类链接且在首页，滚动到对应的分类区域
    if (pathname === '/' && href.startsWith('/category/')) {
      e.preventDefault();
      setActiveItem(href);

      // 提取分类名称
      const categoryId = href.split('/category/')[1];
      const targetElement = document.getElementById(categoryId);
      
      if (targetElement) {
        const offset = 80; // 头部高度约为 72px，额外留出一些空间
        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - offset;
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    }
  };

  // 合并固定菜单项和分类
  const allMenuItems = [
    ...fixedMenuItems,
    ...categories.map(cat => ({
      icon: cat.image.startsWith('http') ? cat.image : `/images${cat.image}`,
      alt: cat.name,
      href: `/category/${cat.name.toLowerCase().replace(/\s+/g, '-')}`
    }))
  ];

  return (
    <>
      {/* 侧边栏内容 */}
      <div className={`fixed left-0 top-[72px] h-[calc(100%-72px)] bg-primary transition-all duration-300 z-[9999] ${isCollapsed ? 'w-0 overflow-hidden' : isSmallScreen ? 'w-14' : 'w-16'}`}>
        <div className={`flex flex-col items-center ${isSmallScreen ? 'pt-0 gap-4' : 'pt-0 gap-6'} pb-4 px-3 h-full`}>
          {allMenuItems.map((item) => (
            <Link 
              href={item.href} 
              key={item.alt}
              onClick={(e) => scrollToSection(e, item.href)}
              className="group relative"
            >
              <div 
                className={`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 overflow-hidden group-hover:scale-110 group-hover:rotate-3 
                  ${activeItem === item.href 
                    ? 'bg-white ring-4 ring-white' 
                    : 'bg-white hover:bg-gray-100'}`}
              >
                <Image 
                  src={item.icon} 
                  alt={item.alt} 
                  width={40} 
                  height={40}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              {/* 自定义 Tooltip */}
              <div className="absolute left-full top-1/2 -translate-y-1/2 ml-3 px-3 py-1.5 bg-black/90 text-white text-sm rounded-md whitespace-nowrap opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out transform translate-x-2 group-hover:translate-x-0 pointer-events-none">
                {item.alt}
                {/* Tooltip 箭头 */}
                <div className="absolute top-1/2 right-full -translate-y-1/2 border-[6px] border-transparent border-r-black/90"></div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* 折叠按钮 - 固定在左下角 */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className={`fixed bottom-4 w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-100 transition-all duration-300 shadow-md z-[9999] ${
          isCollapsed 
            ? 'left-2 rounded-l-full rounded-r-full' 
            : isSmallScreen 
              ? 'left-2' 
              : 'left-3'
        }`}
      >
        <svg
          viewBox="0 0 24 24"
          width="20"
          height="20"
          className={`fill-current text-black transition-transform duration-300 ${isCollapsed ? 'rotate-180' : ''}`}
        >
          <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
        </svg>
      </button>
    </>
  );
}
