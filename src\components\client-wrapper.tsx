'use client';

import { useState, useEffect, ReactNode } from 'react';
import { Sidebar } from "@/components/ui/sidebar";

interface ClientWrapperProps {
  children: ReactNode;
}

export function ClientWrapper({ children }: ClientWrapperProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  // 监听窗口宽度变化
  useEffect(() => {
    const checkWidth = () => {
      setIsSmallScreen(window.innerWidth < 640);
    };
    
    // 初始检查
    checkWidth();
    
    // 添加窗口大小变化事件监听
    window.addEventListener('resize', checkWidth);
    
    // 清理事件监听
    return () => window.removeEventListener('resize', checkWidth);
  }, []);

  // 处理子元素的原始内容
  const childrenContent = (
    <div className={`pt-[30px] sm:pt-[40px] transition-all duration-300 ease-in-out ${
      sidebarCollapsed 
        ? isSmallScreen ? 'ml-0 pl-8' : 'ml-0 pl-8' 
        : isSmallScreen 
          ? 'ml-14 pl-1' 
          : 'ml-16'
    }`}>
      {children}
    </div>
  );

  return (
    <>
      <Sidebar onStateChange={setSidebarCollapsed} />
      {childrenContent}
    </>
  );
} 